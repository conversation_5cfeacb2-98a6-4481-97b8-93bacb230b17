diff --git a/UT_before.txt b/UT_after.txt
index c6697eab..b1e0a2d1 100644
--- a/UT_before.txt
+++ b/UT_after.txt
@@ -1,106 +1,30 @@
 npx hardhat test
-(node:75420) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
+(node:97999) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
 (Use `node --trace-deprecation ...` to show where the warning was created)
-Warning: This declaration shadows an existing declaration.
-  --> contracts/BusinessZoneAccount.sol:88:14:
-   |
-88 |             (bool success, string memory errTmp) = _contractManager.account().hasAccount(accountId);
-   |              ^^^^^^^^^^^^
-Note: The shadowed declaration is here:
-  --> contracts/BusinessZoneAccount.sol:93:10:
-   |
-93 |         (bool success, string memory err) = _hasAccountByZone(zoneId, accountId);
-   |          ^^^^^^^^^^^^
-
-
-Warning: This declaration shadows an existing declaration.
-   --> contracts/BusinessZoneAccount.sol:116:14:
-    |
-116 |             (bool success, string memory errTmp) = _contractManager.account().hasAccount(accountId);
-    |              ^^^^^^^^^^^^
-Note: The shadowed declaration is here:
-   --> contracts/BusinessZoneAccount.sol:121:10:
-    |
-121 |         (bool success, string memory err) = _hasAccountByZone(zoneId, accountId);
-    |          ^^^^^^^^^^^^
-
-
-Warning: Unused function parameter. Remove or comment out the variable name to silence this warning.
-   --> contracts/AccountSyncBridge.sol:132:9:
-    |
-132 |         uint16 zoneId,
-    |         ^^^^^^^^^^^^^
-
-
-Warning: Unused function parameter. Remove or comment out the variable name to silence this warning.
-   --> contracts/BalanceSyncBridge.sol:113:9:
-    |
-113 |         uint16 zoneId,
-    |         ^^^^^^^^^^^^^
-
-
-Warning: Unused function parameter. Remove or comment out the variable name to silence this warning.
-   --> contracts/FinancialCheck.sol:184:9:
-    |
-184 |         bytes32 miscValue1,
-    |         ^^^^^^^^^^^^^^^^^^
-
-
-Warning: Unused local variable.
-   --> contracts/Token.sol:207:28:
-    |
-207 |             (bool success, string memory err) = _contractManager.validator().hasValidator(
-    |                            ^^^^^^^^^^^^^^^^^
-
-
-Warning: Unused function parameter. Remove or comment out the variable name to silence this warning.
-   --> contracts/mocks/AccountMock.sol:145:9:
-    |
-145 |         bytes32 accountId,
-    |         ^^^^^^^^^^^^^^^^^
-
-
-Warning: Unused function parameter. Remove or comment out the variable name to silence this warning.
-   --> contracts/mocks/AccountMock.sol:146:9:
-    |
-146 |         uint256 burnedAmount,
-    |         ^^^^^^^^^^^^^^^^^^^^
-
-
-Warning: Unused function parameter. Remove or comment out the variable name to silence this warning.
-   --> contracts/mocks/AccountMock.sol:147:9:
-    |
-147 |         uint256 burnedBalance,
-    |         ^^^^^^^^^^^^^^^^^^^^^
-
-
-Warning: Unused function parameter. Remove or comment out the variable name to silence this warning.
-   --> contracts/mocks/AccountMock.sol:148:9:
-    |
-148 |         bytes32 traceId
-    |         ^^^^^^^^^^^^^^^
-
-
-Warning: Unused function parameter. Remove or comment out the variable name to silence this warning.
-   --> contracts/renewableEnergyToken/RenewableEnergyToken.sol:366:9:
-    |
-366 |         string memory sortOrder // コントラクト側でソー㠮..
-    |         ^^^^^^^^^^^^^^^^^^^^^^^
-
-
-Warning: Function state mutability can be restricted to pure
-   --> contracts/mocks/BusinessZoneAccountMock.sol:153:5:
-    |
-153 |     function setBizAccountsAll(
-    |     ^ (Relevant source part starts here and spans across multiple lines).
-
-
-Compiled 34 Solidity files successfully (evm target: london).
 
 
   AccessCtrl
     version()
       正常系
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddValidatorAccountIdIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkAddValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"address","name":"validatorEoa","type":"address"}],"name":"checkAddValidatorRoleIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkModAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkModValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"}],"name":"checkSyncAccountStatusIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkValidatorAndAccountExist","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountIdExistenceByValidatorId","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountWithLimits","outputs":[{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"}],"internalType":"struct AccountDataWithLimitData","name":"accountData","type":"tuple"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"}],"name":"getValidatorAccountAllList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct BusinessZoneAccountDataWithZoneId[]","name":"businessZoneAccounts","type":"tuple[]"}],"internalType":"struct AccountDataAll","name":"accountDataAll","type":"tuple"}],"internalType":"struct ValidatorAccountsDataALL[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"string","name":"sortOrder","type":"string"}],"name":"getValidatorAccountList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct ValidatorAccountsData[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorAll","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bool","name":"validatorIdExistence","type":"bool"},{"internalType":"bool","name":"issuerIdLinkedFlag","type":"bool"},{"internalType":"address","name":"validatorEoa","type":"address"},{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bool","name":"accountIdExistenceByValidatorId","type":"bool"}],"internalType":"struct ValidatorAccountsExistence[]","name":"validAccountExistence","type":"tuple[]"}],"internalType":"struct ValidatorAll","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorData","outputs":[{"components":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32[]","name":"accountIds","type":"bytes32[]"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"}],"internalType":"struct ValidatorData","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorIdByIndex","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorIdExistence","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
         ✔ versionが取得できること
     addAdminRole()
       正常系
@@ -127,33 +51,147 @@ Compiled 34 Solidity files successfully (evm target: london).
           ✔ 署名が無効の場合、エラーがスローされること
     addRoleByProv()
       正常系
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddValidatorAccountIdIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkAddValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"address","name":"validatorEoa","type":"address"}],"name":"checkAddValidatorRoleIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkModAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkModValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"}],"name":"checkSyncAccountStatusIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkValidatorAndAccountExist","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountIdExistenceByValidatorId","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountWithLimits","outputs":[{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"}],"internalType":"struct AccountDataWithLimitData","name":"accountData","type":"tuple"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"}],"name":"getValidatorAccountAllList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct BusinessZoneAccountDataWithZoneId[]","name":"businessZoneAccounts","type":"tuple[]"}],"internalType":"struct AccountDataAll","name":"accountDataAll","type":"tuple"}],"internalType":"struct ValidatorAccountsDataALL[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"string","name":"sortOrder","type":"string"}],"name":"getValidatorAccountList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct ValidatorAccountsData[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorAll","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bool","name":"validatorIdExistence","type":"bool"},{"internalType":"bool","name":"issuerIdLinkedFlag","type":"bool"},{"internalType":"address","name":"validatorEoa","type":"address"},{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bool","name":"accountIdExistenceByValidatorId","type":"bool"}],"internalType":"struct ValidatorAccountsExistence[]","name":"validAccountExistence","type":"tuple[]"}],"internalType":"struct ValidatorAll","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorData","outputs":[{"components":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32[]","name":"accountIds","type":"bytes32[]"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"}],"internalType":"struct ValidatorData","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorIdByIndex","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorIdExistence","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
         Providerのコントラクトアドレスにaccounts[7]のアドレスが設定されている状態
           ✔ 権限が付与されること
         accounts[5]に権限が付与されている状態
           ✔ 同一アカウントに同一権限を付与した場合、RoleGrantedイベントが発火されないこと
       準正常系
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddValidatorAccountIdIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkAddValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"address","name":"validatorEoa","type":"address"}],"name":"checkAddValidatorRoleIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkModAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkModValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"}],"name":"checkSyncAccountStatusIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkValidatorAndAccountExist","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountIdExistenceByValidatorId","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountWithLimits","outputs":[{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"}],"internalType":"struct AccountDataWithLimitData","name":"accountData","type":"tuple"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"}],"name":"getValidatorAccountAllList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct BusinessZoneAccountDataWithZoneId[]","name":"businessZoneAccounts","type":"tuple[]"}],"internalType":"struct AccountDataAll","name":"accountDataAll","type":"tuple"}],"internalType":"struct ValidatorAccountsDataALL[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"string","name":"sortOrder","type":"string"}],"name":"getValidatorAccountList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct ValidatorAccountsData[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorAll","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bool","name":"validatorIdExistence","type":"bool"},{"internalType":"bool","name":"issuerIdLinkedFlag","type":"bool"},{"internalType":"address","name":"validatorEoa","type":"address"},{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bool","name":"accountIdExistenceByValidatorId","type":"bool"}],"internalType":"struct ValidatorAccountsExistence[]","name":"validAccountExistence","type":"tuple[]"}],"internalType":"struct ValidatorAll","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorData","outputs":[{"components":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32[]","name":"accountIds","type":"bytes32[]"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"}],"internalType":"struct ValidatorData","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorIdByIndex","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorIdExistence","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
         Providerのコントラクトアドレスにaccounts[7]のアドレスが設定されている状態
           ✔ 権限にAdmin権限を指定した場合、エラーがスローされること
           ✔ 呼び出し元がProviderではない場合、エラーがスローされること
           ✔ Admin権限でない署名の場合、エラーがスローされること
     addRoleByIssuer()
       正常系
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddValidatorAccountIdIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkAddValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"address","name":"validatorEoa","type":"address"}],"name":"checkAddValidatorRoleIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkModAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkModValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"}],"name":"checkSyncAccountStatusIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkValidatorAndAccountExist","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountIdExistenceByValidatorId","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountWithLimits","outputs":[{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"}],"internalType":"struct AccountDataWithLimitData","name":"accountData","type":"tuple"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"}],"name":"getValidatorAccountAllList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct BusinessZoneAccountDataWithZoneId[]","name":"businessZoneAccounts","type":"tuple[]"}],"internalType":"struct AccountDataAll","name":"accountDataAll","type":"tuple"}],"internalType":"struct ValidatorAccountsDataALL[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"string","name":"sortOrder","type":"string"}],"name":"getValidatorAccountList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct ValidatorAccountsData[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorAll","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bool","name":"validatorIdExistence","type":"bool"},{"internalType":"bool","name":"issuerIdLinkedFlag","type":"bool"},{"internalType":"address","name":"validatorEoa","type":"address"},{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bool","name":"accountIdExistenceByValidatorId","type":"bool"}],"internalType":"struct ValidatorAccountsExistence[]","name":"validAccountExistence","type":"tuple[]"}],"internalType":"struct ValidatorAll","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorData","outputs":[{"components":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32[]","name":"accountIds","type":"bytes32[]"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"}],"internalType":"struct ValidatorData","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorIdByIndex","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorIdExistence","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
         Issuerのコントラクトアドレスにaccounts[7]のアドレスが設定されている状態
           ✔ 権限が付与されること
         accounts[5]に権限が付与されている状態
           ✔ 同一アカウントに同一権限を付与した場合、RoleGrantedイベントが発火されないこと
       準正常系
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddValidatorAccountIdIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkAddValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"address","name":"validatorEoa","type":"address"}],"name":"checkAddValidatorRoleIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkModAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkModValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"}],"name":"checkSyncAccountStatusIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkValidatorAndAccountExist","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountIdExistenceByValidatorId","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountWithLimits","outputs":[{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"}],"internalType":"struct AccountDataWithLimitData","name":"accountData","type":"tuple"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"}],"name":"getValidatorAccountAllList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct BusinessZoneAccountDataWithZoneId[]","name":"businessZoneAccounts","type":"tuple[]"}],"internalType":"struct AccountDataAll","name":"accountDataAll","type":"tuple"}],"internalType":"struct ValidatorAccountsDataALL[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"string","name":"sortOrder","type":"string"}],"name":"getValidatorAccountList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct ValidatorAccountsData[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorAll","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bool","name":"validatorIdExistence","type":"bool"},{"internalType":"bool","name":"issuerIdLinkedFlag","type":"bool"},{"internalType":"address","name":"validatorEoa","type":"address"},{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bool","name":"accountIdExistenceByValidatorId","type":"bool"}],"internalType":"struct ValidatorAccountsExistence[]","name":"validAccountExistence","type":"tuple[]"}],"internalType":"struct ValidatorAll","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorData","outputs":[{"components":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32[]","name":"accountIds","type":"bytes32[]"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"}],"internalType":"struct ValidatorData","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorIdByIndex","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorIdExistence","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
         Issuerのコントラクトアドレスにaccounts[7]のアドレスが設定されている状態
           ✔ 権限にAdmin権限を指定した場合、エラーがスローされること
           ✔ 呼び出し元がIssuerではない場合、エラーがスローされること
           ✔ Admin権限でない署名の場合、エラーがスローされること
     addRoleByValidator()
       正常系
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddValidatorAccountIdIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkAddValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"address","name":"validatorEoa","type":"address"}],"name":"checkAddValidatorRoleIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkModAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkModValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"}],"name":"checkSyncAccountStatusIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkValidatorAndAccountExist","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountIdExistenceByValidatorId","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountWithLimits","outputs":[{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"}],"internalType":"struct AccountDataWithLimitData","name":"accountData","type":"tuple"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"}],"name":"getValidatorAccountAllList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct BusinessZoneAccountDataWithZoneId[]","name":"businessZoneAccounts","type":"tuple[]"}],"internalType":"struct AccountDataAll","name":"accountDataAll","type":"tuple"}],"internalType":"struct ValidatorAccountsDataALL[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"string","name":"sortOrder","type":"string"}],"name":"getValidatorAccountList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct ValidatorAccountsData[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorAll","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bool","name":"validatorIdExistence","type":"bool"},{"internalType":"bool","name":"issuerIdLinkedFlag","type":"bool"},{"internalType":"address","name":"validatorEoa","type":"address"},{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bool","name":"accountIdExistenceByValidatorId","type":"bool"}],"internalType":"struct ValidatorAccountsExistence[]","name":"validAccountExistence","type":"tuple[]"}],"internalType":"struct ValidatorAll","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorData","outputs":[{"components":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32[]","name":"accountIds","type":"bytes32[]"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"}],"internalType":"struct ValidatorData","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorIdByIndex","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorIdExistence","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
         Validatorのコントラクトアドレスにaccounts[7]のアドレスが設定されている状態
           ✔ 権限が付与されること
         accounts[5]に権限が付与されている状態
           ✔ 同一アカウントに同一権限を付与した場合、RoleGrantedイベントが発火されないこと
       準正常系
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddValidatorAccountIdIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkAddValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"address","name":"validatorEoa","type":"address"}],"name":"checkAddValidatorRoleIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkModAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkModValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"}],"name":"checkSyncAccountStatusIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkValidatorAndAccountExist","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountIdExistenceByValidatorId","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountWithLimits","outputs":[{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"}],"internalType":"struct AccountDataWithLimitData","name":"accountData","type":"tuple"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"}],"name":"getValidatorAccountAllList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct BusinessZoneAccountDataWithZoneId[]","name":"businessZoneAccounts","type":"tuple[]"}],"internalType":"struct AccountDataAll","name":"accountDataAll","type":"tuple"}],"internalType":"struct ValidatorAccountsDataALL[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"string","name":"sortOrder","type":"string"}],"name":"getValidatorAccountList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct ValidatorAccountsData[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorAll","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bool","name":"validatorIdExistence","type":"bool"},{"internalType":"bool","name":"issuerIdLinkedFlag","type":"bool"},{"internalType":"address","name":"validatorEoa","type":"address"},{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bool","name":"accountIdExistenceByValidatorId","type":"bool"}],"internalType":"struct ValidatorAccountsExistence[]","name":"validAccountExistence","type":"tuple[]"}],"internalType":"struct ValidatorAll","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorData","outputs":[{"components":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32[]","name":"accountIds","type":"bytes32[]"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"}],"internalType":"struct ValidatorData","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorIdByIndex","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorIdExistence","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
         Validatorのコントラクトアドレスにaccounts[7]のアドレスが設定されている状態
           ✔ 権限にAdmin権限を指定した場合、エラーがスローされること
           ✔ 呼び出し元がValidatorではない場合、エラーがスローされること
@@ -177,6 +215,25 @@ Compiled 34 Solidity files successfully (evm target: london).
           ✔ 署名が無効（not signature）の場合、エラーがスローされること
     checkRole()
       正常系
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddValidatorAccountIdIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkAddValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"address","name":"validatorEoa","type":"address"}],"name":"checkAddValidatorRoleIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkModAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkModValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"}],"name":"checkSyncAccountStatusIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkValidatorAndAccountExist","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountIdExistenceByValidatorId","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountWithLimits","outputs":[{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"}],"internalType":"struct AccountDataWithLimitData","name":"accountData","type":"tuple"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"}],"name":"getValidatorAccountAllList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct BusinessZoneAccountDataWithZoneId[]","name":"businessZoneAccounts","type":"tuple[]"}],"internalType":"struct AccountDataAll","name":"accountDataAll","type":"tuple"}],"internalType":"struct ValidatorAccountsDataALL[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"string","name":"sortOrder","type":"string"}],"name":"getValidatorAccountList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct ValidatorAccountsData[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorAll","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bool","name":"validatorIdExistence","type":"bool"},{"internalType":"bool","name":"issuerIdLinkedFlag","type":"bool"},{"internalType":"address","name":"validatorEoa","type":"address"},{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bool","name":"accountIdExistenceByValidatorId","type":"bool"}],"internalType":"struct ValidatorAccountsExistence[]","name":"validAccountExistence","type":"tuple[]"}],"internalType":"struct ValidatorAll","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorData","outputs":[{"components":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32[]","name":"accountIds","type":"bytes32[]"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"}],"internalType":"struct ValidatorData","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorIdByIndex","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorIdExistence","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
         アカウントにProvider権限が付与されている状態
           ✔ 指定した権限がある場合、trueが返されること
           ✔ Provider権限でない署名の場合、falseが返されること
@@ -193,6 +250,25 @@ Compiled 34 Solidity files successfully (evm target: london).
         ✔ Account署名検証でアドレスが一致しない場合、GS0002エラーが返されること
     _checkValidatorSig_deep()
       準正常系
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddValidatorAccountIdIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkAddValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"address","name":"validatorEoa","type":"address"}],"name":"checkAddValidatorRoleIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkModAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkModValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"}],"name":"checkSyncAccountStatusIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkValidatorAndAccountExist","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountIdExistenceByValidatorId","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountWithLimits","outputs":[{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"}],"internalType":"struct AccountDataWithLimitData","name":"accountData","type":"tuple"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"}],"name":"getValidatorAccountAllList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct BusinessZoneAccountDataWithZoneId[]","name":"businessZoneAccounts","type":"tuple[]"}],"internalType":"struct AccountDataAll","name":"accountDataAll","type":"tuple"}],"internalType":"struct ValidatorAccountsDataALL[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"string","name":"sortOrder","type":"string"}],"name":"getValidatorAccountList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct ValidatorAccountsData[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorAll","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bool","name":"validatorIdExistence","type":"bool"},{"internalType":"bool","name":"issuerIdLinkedFlag","type":"bool"},{"internalType":"address","name":"validatorEoa","type":"address"},{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bool","name":"accountIdExistenceByValidatorId","type":"bool"}],"internalType":"struct ValidatorAccountsExistence[]","name":"validAccountExistence","type":"tuple[]"}],"internalType":"struct ValidatorAll","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorData","outputs":[{"components":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32[]","name":"accountIds","type":"bytes32[]"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"}],"internalType":"struct ValidatorData","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorIdByIndex","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorIdExistence","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
         ✔ 署名期限切れの場合、GS0003エラーが返されること
         ✔ validatorIdのアドレスが登録されている場合、正常終了すること
         ✔ validatorIdのアドレスが一致しない場合、GS0002エラーが返されること
@@ -207,26 +283,140 @@ Compiled 34 Solidity files successfully (evm target: london).
           ✔ 呼び出し元がDEFAULT_ADMIN権限ではない場合、エラーがスローされること
     delProviderRole()
       正常系
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddValidatorAccountIdIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkAddValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"address","name":"validatorEoa","type":"address"}],"name":"checkAddValidatorRoleIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkModAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkModValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"}],"name":"checkSyncAccountStatusIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkValidatorAndAccountExist","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountIdExistenceByValidatorId","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountWithLimits","outputs":[{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"}],"internalType":"struct AccountDataWithLimitData","name":"accountData","type":"tuple"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"}],"name":"getValidatorAccountAllList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct BusinessZoneAccountDataWithZoneId[]","name":"businessZoneAccounts","type":"tuple[]"}],"internalType":"struct AccountDataAll","name":"accountDataAll","type":"tuple"}],"internalType":"struct ValidatorAccountsDataALL[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"string","name":"sortOrder","type":"string"}],"name":"getValidatorAccountList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct ValidatorAccountsData[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorAll","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bool","name":"validatorIdExistence","type":"bool"},{"internalType":"bool","name":"issuerIdLinkedFlag","type":"bool"},{"internalType":"address","name":"validatorEoa","type":"address"},{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bool","name":"accountIdExistenceByValidatorId","type":"bool"}],"internalType":"struct ValidatorAccountsExistence[]","name":"validAccountExistence","type":"tuple[]"}],"internalType":"struct ValidatorAll","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorData","outputs":[{"components":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32[]","name":"accountIds","type":"bytes32[]"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"}],"internalType":"struct ValidatorData","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorIdByIndex","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorIdExistence","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
         アカウントにProvider権限が付与されている状態
           ✔ Provider権限のないアカウントを指定した場合、RoleRevokedイベントが発火されないこと
           ✔ 権限が削除されること
       準正常系
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddValidatorAccountIdIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkAddValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"address","name":"validatorEoa","type":"address"}],"name":"checkAddValidatorRoleIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkModAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkModValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"}],"name":"checkSyncAccountStatusIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkValidatorAndAccountExist","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountIdExistenceByValidatorId","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountWithLimits","outputs":[{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"}],"internalType":"struct AccountDataWithLimitData","name":"accountData","type":"tuple"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"}],"name":"getValidatorAccountAllList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct BusinessZoneAccountDataWithZoneId[]","name":"businessZoneAccounts","type":"tuple[]"}],"internalType":"struct AccountDataAll","name":"accountDataAll","type":"tuple"}],"internalType":"struct ValidatorAccountsDataALL[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"string","name":"sortOrder","type":"string"}],"name":"getValidatorAccountList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct ValidatorAccountsData[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorAll","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bool","name":"validatorIdExistence","type":"bool"},{"internalType":"bool","name":"issuerIdLinkedFlag","type":"bool"},{"internalType":"address","name":"validatorEoa","type":"address"},{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bool","name":"accountIdExistenceByValidatorId","type":"bool"}],"internalType":"struct ValidatorAccountsExistence[]","name":"validAccountExistence","type":"tuple[]"}],"internalType":"struct ValidatorAll","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorData","outputs":[{"components":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32[]","name":"accountIds","type":"bytes32[]"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"}],"internalType":"struct ValidatorData","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorIdByIndex","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorIdExistence","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
         アカウントにProvider権限が付与されている状態
           ✔ 呼び出し元がDEFAULT_ADMIN権限ではない場合、エラーがスローされること
     delIssuerRole()
       正常系
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddValidatorAccountIdIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkAddValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"address","name":"validatorEoa","type":"address"}],"name":"checkAddValidatorRoleIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkModAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkModValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"}],"name":"checkSyncAccountStatusIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkValidatorAndAccountExist","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountIdExistenceByValidatorId","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountWithLimits","outputs":[{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"}],"internalType":"struct AccountDataWithLimitData","name":"accountData","type":"tuple"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"}],"name":"getValidatorAccountAllList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct BusinessZoneAccountDataWithZoneId[]","name":"businessZoneAccounts","type":"tuple[]"}],"internalType":"struct AccountDataAll","name":"accountDataAll","type":"tuple"}],"internalType":"struct ValidatorAccountsDataALL[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"string","name":"sortOrder","type":"string"}],"name":"getValidatorAccountList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct ValidatorAccountsData[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorAll","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bool","name":"validatorIdExistence","type":"bool"},{"internalType":"bool","name":"issuerIdLinkedFlag","type":"bool"},{"internalType":"address","name":"validatorEoa","type":"address"},{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bool","name":"accountIdExistenceByValidatorId","type":"bool"}],"internalType":"struct ValidatorAccountsExistence[]","name":"validAccountExistence","type":"tuple[]"}],"internalType":"struct ValidatorAll","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorData","outputs":[{"components":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32[]","name":"accountIds","type":"bytes32[]"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"}],"internalType":"struct ValidatorData","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorIdByIndex","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorIdExistence","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
         アカウントにIssuer権限が付与されている状態
           ✔ Issuer権限のないアカウントを指定した場合、RoleRevokedイベントが発火されないこと
           ✔ 権限が削除されること
       準正常系
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddValidatorAccountIdIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkAddValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"address","name":"validatorEoa","type":"address"}],"name":"checkAddValidatorRoleIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkModAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkModValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"}],"name":"checkSyncAccountStatusIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkValidatorAndAccountExist","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountIdExistenceByValidatorId","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountWithLimits","outputs":[{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"}],"internalType":"struct AccountDataWithLimitData","name":"accountData","type":"tuple"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"}],"name":"getValidatorAccountAllList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct BusinessZoneAccountDataWithZoneId[]","name":"businessZoneAccounts","type":"tuple[]"}],"internalType":"struct AccountDataAll","name":"accountDataAll","type":"tuple"}],"internalType":"struct ValidatorAccountsDataALL[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"string","name":"sortOrder","type":"string"}],"name":"getValidatorAccountList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct ValidatorAccountsData[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorAll","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bool","name":"validatorIdExistence","type":"bool"},{"internalType":"bool","name":"issuerIdLinkedFlag","type":"bool"},{"internalType":"address","name":"validatorEoa","type":"address"},{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bool","name":"accountIdExistenceByValidatorId","type":"bool"}],"internalType":"struct ValidatorAccountsExistence[]","name":"validAccountExistence","type":"tuple[]"}],"internalType":"struct ValidatorAll","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorData","outputs":[{"components":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32[]","name":"accountIds","type":"bytes32[]"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"}],"internalType":"struct ValidatorData","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorIdByIndex","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorIdExistence","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
         アカウントにIssuer権限が付与されている状態
           ✔ 呼び出し元がDEFAULT_ADMIN権限ではない場合、エラーがスローされること
     delValidatorRole()
       正常系
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddValidatorAccountIdIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkAddValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"address","name":"validatorEoa","type":"address"}],"name":"checkAddValidatorRoleIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkModAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkModValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"}],"name":"checkSyncAccountStatusIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkValidatorAndAccountExist","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountIdExistenceByValidatorId","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountWithLimits","outputs":[{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"}],"internalType":"struct AccountDataWithLimitData","name":"accountData","type":"tuple"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"}],"name":"getValidatorAccountAllList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct BusinessZoneAccountDataWithZoneId[]","name":"businessZoneAccounts","type":"tuple[]"}],"internalType":"struct AccountDataAll","name":"accountDataAll","type":"tuple"}],"internalType":"struct ValidatorAccountsDataALL[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"string","name":"sortOrder","type":"string"}],"name":"getValidatorAccountList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct ValidatorAccountsData[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorAll","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bool","name":"validatorIdExistence","type":"bool"},{"internalType":"bool","name":"issuerIdLinkedFlag","type":"bool"},{"internalType":"address","name":"validatorEoa","type":"address"},{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bool","name":"accountIdExistenceByValidatorId","type":"bool"}],"internalType":"struct ValidatorAccountsExistence[]","name":"validAccountExistence","type":"tuple[]"}],"internalType":"struct ValidatorAll","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorData","outputs":[{"components":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32[]","name":"accountIds","type":"bytes32[]"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"}],"internalType":"struct ValidatorData","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorIdByIndex","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorIdExistence","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
         アカウントにValidator権限が付与されている状態
           ✔ Validator権限のないアカウントを指定した場合、RoleRevokedイベントが発火されないこと
           ✔ 権限が削除されること
       準正常系
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddValidatorAccountIdIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkAddValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"address","name":"validatorEoa","type":"address"}],"name":"checkAddValidatorRoleIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkModAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkModValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"}],"name":"checkSyncAccountStatusIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkValidatorAndAccountExist","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountIdExistenceByValidatorId","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountWithLimits","outputs":[{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"}],"internalType":"struct AccountDataWithLimitData","name":"accountData","type":"tuple"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"}],"name":"getValidatorAccountAllList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct BusinessZoneAccountDataWithZoneId[]","name":"businessZoneAccounts","type":"tuple[]"}],"internalType":"struct AccountDataAll","name":"accountDataAll","type":"tuple"}],"internalType":"struct ValidatorAccountsDataALL[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"string","name":"sortOrder","type":"string"}],"name":"getValidatorAccountList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct ValidatorAccountsData[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorAll","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bool","name":"validatorIdExistence","type":"bool"},{"internalType":"bool","name":"issuerIdLinkedFlag","type":"bool"},{"internalType":"address","name":"validatorEoa","type":"address"},{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bool","name":"accountIdExistenceByValidatorId","type":"bool"}],"internalType":"struct ValidatorAccountsExistence[]","name":"validAccountExistence","type":"tuple[]"}],"internalType":"struct ValidatorAll","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorData","outputs":[{"components":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32[]","name":"accountIds","type":"bytes32[]"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"}],"internalType":"struct ValidatorData","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorIdByIndex","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorIdExistence","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
         アカウントにValidator権限が付与されている状態
           ✔ 呼び出し元がDEFAULT_ADMIN権限ではない場合、エラーがスローされること
 
@@ -366,7 +556,7 @@ Compiled 34 Solidity files successfully (evm target: london).
     forceBurn()
       正常系
         issuerRole, accountが登録されている状態
-          ✔ Accountの残高を強制償却できること,totalSupplyが減算されること
+          ✔ Accountの残高を強制償却できること,totalSupplyが減算されること (52ms)
       準正常系
         issuerRole, accountが登録されている状態
           ✔ Issuerコントラクト以外から呼び出した場合、エラーがスローされること
@@ -703,7 +893,7 @@ Compiled 34 Solidity files successfully (evm target: london).
           ✔ toAccountのステータスが"active"以外である場合、エラーが返却されること
           ✔ 日次の累積限度額を超えている場合、エラーが返却されること
           ✔ 移転限度額を超えている場合、エラーが返却されること
-          ✔ 日次の移転累積限度額を超えている場合、エラーが返却されること (39ms)
+          ✔ 日次の移転累積限度額を超えている場合、エラーが返却されること
           ✔ 日付が変わった場合、移転累積限度額を超えない金額では正常に処理されること
           ✔ 日付が変わった場合、移転累積限度額を超える金額ではエラーが返却されること
           ✔ アカウント署名が無効である場合、エラーが返却されること
@@ -1093,7 +1283,7 @@ Compiled 34 Solidity files successfully (evm target: london).
     getAccountList()
       正常系
         issuerRole, accountが登録されている状態
-          ✔ 指定したissuerIdに紐づくaccountリストが取得できること (79ms)
+          ✔ 指定したissuerIdに紐づくaccountリストが取得できること (53ms)
           ✔ inAccountIdsに存在しないaccountIdが含まれる場合、空データが取得できること
           ✔ 空のaccountリストでリクエストした場合、空リストが取得できること
           ✔ issuerに紐づくaccountがない場合、エラーがスローされること
@@ -1680,7 +1870,7 @@ Compiled 34 Solidity files successfully (evm target: london).
           ✔ offset1, limit10を指定した場合、2ページ目1項目目から10件取得できること
           ✔ offset1, limit2を指定した場合、2ページ目2項目目から2件取得できること
           ✔ 最後の1件が取得できること
-          ✔ limitが取得上限(100件)以下の場合、データが取得ができること (64ms)
+          ✔ limitが取得上限(100件)以下の場合、データが取得ができること (66ms)
           ✔ limitが0の場合、空リストが取得できること
           ✔ limitが取得上限(100件)より大きい場合、エラーが返されること
           ✔ offsetが登録されている件数以上の場合、エラーが返されること
@@ -1700,11 +1890,11 @@ Compiled 34 Solidity files successfully (evm target: london).
         accountが登録されていない状態
           ✔ 空リストが取得できること
         Accountsが登録されている状態
-          ✔ offset0, limit20を指定した場合、1ページ目1項目目から20件取得できること (72ms)
+          ✔ offset0, limit20を指定した場合、1ページ目1項目目から20件取得できること (70ms)
           ✔ offset1, limit10を指定した場合、2項目目から10件取得できること
           ✔ offset1, limit2を指定した場合、3項目目から2件取得できること
           ✔ 最後の1件が取得できること
-          ✔ limitが取得上限(1000件)以下の場合、データが取得ができること (72ms)
+          ✔ limitが取得上限(1000件)以下の場合、データが取得ができること (70ms)
           ✔ limitが0の場合、空リストが取得できること
           ✔ limitが取得上限(1000件)より大きい場合、エラーが返されること
           ✔ offsetが登録されている件数以上の場合、エラーが返されること
@@ -1778,7 +1968,7 @@ Compiled 34 Solidity files successfully (evm target: london).
     restoreValidators()
       正常系
         初期状態
-          ✔ 全てのissuers(20件)が登録できること (108ms)
+          ✔ 全てのissuers(20件)が登録できること (129ms)
       準正常系
         初期状態
           ✔ Admin権限がない場合、エラーがスローされること
@@ -1797,17 +1987,17 @@ Compiled 34 Solidity files successfully (evm target: london).
     restoreAccounts()
       正常系
         初期状態
-          ✔ 全てのaccounts(20件)が登録できること (142ms)
+          ✔ 全てのaccounts(20件)が登録できること (137ms)
       準正常系
         初期状態
           ✔ Admin権限がない場合、エラーがスローされること (55ms)
-          ✔ 署名無効の場合、エラーがスローされること (55ms)
-          ✔ 署名期限切れの場合、エラーがスローされること (55ms)
+          ✔ 署名無効の場合、エラーがスローされること (54ms)
+          ✔ 署名期限切れの場合、エラーがスローされること (58ms)
           ✔ 異常な値が入力された時にfails
     restoreFinancialZoneAccounts()
       正常系
         初期状態
-          ✔ 全てのaccounts(20件)が登録できること (61ms)
+          ✔ 全てのaccounts(20件)が登録できること (62ms)
       準正常系
         初期状態
           ✔ Admin権限がない場合、エラーがスローされること
@@ -1817,7 +2007,7 @@ Compiled 34 Solidity files successfully (evm target: london).
     restoreIssuers()
       正常系
         初期状態
-          ✔ 全てのissuers(20件)が登録できること (99ms)
+          ✔ 全てのissuers(20件)が登録できること (96ms)
       準正常系
         初期状態
           ✔ Admin権限がない場合、エラーがスローされること
@@ -1838,7 +2028,7 @@ Compiled 34 Solidity files successfully (evm target: london).
       正常系
         初期状態
           初期状態
-            ✔ 全てのbusinessZoneAccount(20件)が登録できること (41ms)
+            ✔ 全てのbusinessZoneAccount(20件)が登録できること (40ms)
       準正常系
         初期状態
           ✔ businessZoneAccountが登録されないこと
@@ -2518,7 +2708,7 @@ Compiled 34 Solidity files successfully (evm target: london).
     getValidatorAll()
       正常系
         Validatorsが登録されている状態
-          ✔ 登録したValidatorsを取得できること (67ms)
+          ✔ 登録したValidatorsを取得できること (69ms)
     setValidatorAll()
       正常系
         初期状態
@@ -2540,9 +2730,9 @@ Compiled 34 Solidity files successfully (evm target: london).
         ✔ offset0, limit10を指定した場合、1要素目から10件取得できること
         ✔ offset2, limit2を指定した場合、3要素目から2件取得できること
         ✔ 最後の1件が取得できること
-        ✔ limitが取得上限(100件)以下の場合、accountリストが取得できること (limit 100)
+        ✔ limitが取得上限(100件)以下の場合、accountリストが取得できること (limit 100) (38ms)
         ✔ limitが0の場合、空リストが取得できること
-        ✔ limitを登録数以上に指定した場合、登録されている要素数の範囲内でのみ取得できること (40ms)
+        ✔ limitを登録数以上に指定した場合、登録されている要素数の範囲内でのみ取得できること
         ✔ limitが取得上限(100件)より大きい場合、エラーが返されること
         ✔ offsetが登録されている件数以上の場合、エラーが返されること
         ✔ offsetが登録されている件数と同じの場合、エラーが返されること
