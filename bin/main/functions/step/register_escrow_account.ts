import { execSync } from 'child_process'
import path from 'path'
import { loadAwsProf } from '../../../common/load_aws_prof'
import { loadEnvVars } from '../../../common/load_env_vars'
import { message, SRC_ZONE_ID, ESCROW_ACCOUNT } from '../../../common/utils'

// スクリプト名の取得
const scriptName = path.basename(__filename)

/**
 * エスクローアカウントを登録する
 * @param network 環境名
 * @param bizZoneCount ビジネスゾーン数
 */
export async function registerEscrowAccount(network: string, bizZoneCount: number) {
  try {
    if (!network) {
      message('err', 'NETWORK environment variable is not set.')
      process.exit(1)
    }

    // AWS_PROFILEの設定
    const profile = loadAwsProf(network)

    // 環境変数の読み出しとエクスポート
    loadEnvVars(profile)

    if (!process.env.ZONE_ID) {
      message('err', 'ZONE_ID environment variable is not set.')
      process.exit(1)
    }

    const zoneId = Number(process.env.ZONE_ID)

    // ZoneIDが3001~3999だったら処理をスキップする
    if (zoneId >= 3001 && zoneId <= 3999) {
      message('info', `Skipping ${scriptName} as ZONE_ID is 3001~3999.`)
      process.exit(0)
    }

    // 指定されたBizZoneの数だけエスクローアカウントの登録処理を行う
    for (let i = 1; i <= bizZoneCount; i++) {
      const dstZoneId = 3000 + i
      message('info', `Registering escrow id to bridge for Zone:${dstZoneId}`)

      const command = `npx hardhat registerEscrowAcc --src-zone-id ${SRC_ZONE_ID} --dst-zone-id ${dstZoneId} --escrow-account ${ESCROW_ACCOUNT} --network ${network}`

      execSync(command, { stdio: 'inherit' })
    }
    message('success', `Successfully registered the escrow account for network: ${network} completed.`)
  } catch (err) {
    message('err', `Failed to execute Hardhat command: ${(err as Error).message}`)
    process.exit(1)
  }
}

// 直接実行用
if (require.main === module) {
  // 引数チェック
  const network = process.argv[2]
  const bizZoneCount = process.argv[3]
  if (!network || !bizZoneCount) {
    message('err', 'Please specify both NETWORK and BIZ_ZONE_COUNT as arguments.')
    console.log(`Usage: npx ts-node bin/main/functions/step/${scriptName} <NETWORK> <BIZ_ZONE_COUNT>`)
    process.exit(1)
  }

  registerEscrowAccount(network, Number(bizZoneCount)).catch((err) => {
    console.error('[ERROR]', err)
    process.exit(1)
  })
}
