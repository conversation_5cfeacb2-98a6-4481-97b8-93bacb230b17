npx hardhat test
(node:97999) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)


  AccessCtrl
    version()
      正常系
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddValidatorAccountIdIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkAddValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"address","name":"validatorEoa","type":"address"}],"name":"checkAddValidatorRoleIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkModAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkModValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"}],"name":"checkSyncAccountStatusIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkValidatorAndAccountExist","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountIdExistenceByValidatorId","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountWithLimits","outputs":[{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"}],"internalType":"struct AccountDataWithLimitData","name":"accountData","type":"tuple"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"}],"name":"getValidatorAccountAllList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct BusinessZoneAccountDataWithZoneId[]","name":"businessZoneAccounts","type":"tuple[]"}],"internalType":"struct AccountDataAll","name":"accountDataAll","type":"tuple"}],"internalType":"struct ValidatorAccountsDataALL[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"string","name":"sortOrder","type":"string"}],"name":"getValidatorAccountList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct ValidatorAccountsData[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorAll","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bool","name":"validatorIdExistence","type":"bool"},{"internalType":"bool","name":"issuerIdLinkedFlag","type":"bool"},{"internalType":"address","name":"validatorEoa","type":"address"},{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bool","name":"accountIdExistenceByValidatorId","type":"bool"}],"internalType":"struct ValidatorAccountsExistence[]","name":"validAccountExistence","type":"tuple[]"}],"internalType":"struct ValidatorAll","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorData","outputs":[{"components":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32[]","name":"accountIds","type":"bytes32[]"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"}],"internalType":"struct ValidatorData","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorIdByIndex","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorIdExistence","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
        ✔ versionが取得できること
    addAdminRole()
      正常系
        初期状態
          ✔ Admin権限が追加できること
        accounts[1]にAdmin権限が付与されている状態
          ✔ RoleGrantedイベントが発火されないこと
      準正常系
        初期状態
          ✔ Admin権限でない署名の場合、エラーがスローされること
          ✔ 署名が無効の場合、エラーがスローされること
          ✔ 署名が無効（not signature）の場合、エラーがスローされること
    addRole()
      正常系
        アカウントにAdmin権限が付与されている状態
          ✔ 権限が付与されること
        accounts[2]に権限が付与されている状態
          ✔ 同一アカウントに同一権限を付与した場合、RoleGrantedイベントが発火されないこと
      準正常系
        アカウントにAdmin権限が付与されている状態
          ✔ 権限にAdmin権限を指定した場合、エラーがスローされること
          ✔ 署名期限切れの場合、エラーがスローされること
          ✔ Admin権限でない署名の場合、エラーがスローされること
          ✔ 署名が無効の場合、エラーがスローされること
    addRoleByProv()
      正常系
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddValidatorAccountIdIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkAddValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"address","name":"validatorEoa","type":"address"}],"name":"checkAddValidatorRoleIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkModAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkModValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"}],"name":"checkSyncAccountStatusIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkValidatorAndAccountExist","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountIdExistenceByValidatorId","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountWithLimits","outputs":[{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"}],"internalType":"struct AccountDataWithLimitData","name":"accountData","type":"tuple"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"}],"name":"getValidatorAccountAllList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct BusinessZoneAccountDataWithZoneId[]","name":"businessZoneAccounts","type":"tuple[]"}],"internalType":"struct AccountDataAll","name":"accountDataAll","type":"tuple"}],"internalType":"struct ValidatorAccountsDataALL[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"string","name":"sortOrder","type":"string"}],"name":"getValidatorAccountList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct ValidatorAccountsData[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorAll","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bool","name":"validatorIdExistence","type":"bool"},{"internalType":"bool","name":"issuerIdLinkedFlag","type":"bool"},{"internalType":"address","name":"validatorEoa","type":"address"},{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bool","name":"accountIdExistenceByValidatorId","type":"bool"}],"internalType":"struct ValidatorAccountsExistence[]","name":"validAccountExistence","type":"tuple[]"}],"internalType":"struct ValidatorAll","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorData","outputs":[{"components":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32[]","name":"accountIds","type":"bytes32[]"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"}],"internalType":"struct ValidatorData","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorIdByIndex","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorIdExistence","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
        Providerのコントラクトアドレスにaccounts[7]のアドレスが設定されている状態
          ✔ 権限が付与されること
        accounts[5]に権限が付与されている状態
          ✔ 同一アカウントに同一権限を付与した場合、RoleGrantedイベントが発火されないこと
      準正常系
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddValidatorAccountIdIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkAddValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"address","name":"validatorEoa","type":"address"}],"name":"checkAddValidatorRoleIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkModAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkModValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"}],"name":"checkSyncAccountStatusIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkValidatorAndAccountExist","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountIdExistenceByValidatorId","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountWithLimits","outputs":[{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"}],"internalType":"struct AccountDataWithLimitData","name":"accountData","type":"tuple"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"}],"name":"getValidatorAccountAllList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct BusinessZoneAccountDataWithZoneId[]","name":"businessZoneAccounts","type":"tuple[]"}],"internalType":"struct AccountDataAll","name":"accountDataAll","type":"tuple"}],"internalType":"struct ValidatorAccountsDataALL[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"string","name":"sortOrder","type":"string"}],"name":"getValidatorAccountList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct ValidatorAccountsData[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorAll","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bool","name":"validatorIdExistence","type":"bool"},{"internalType":"bool","name":"issuerIdLinkedFlag","type":"bool"},{"internalType":"address","name":"validatorEoa","type":"address"},{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bool","name":"accountIdExistenceByValidatorId","type":"bool"}],"internalType":"struct ValidatorAccountsExistence[]","name":"validAccountExistence","type":"tuple[]"}],"internalType":"struct ValidatorAll","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorData","outputs":[{"components":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32[]","name":"accountIds","type":"bytes32[]"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"}],"internalType":"struct ValidatorData","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorIdByIndex","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorIdExistence","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
        Providerのコントラクトアドレスにaccounts[7]のアドレスが設定されている状態
          ✔ 権限にAdmin権限を指定した場合、エラーがスローされること
          ✔ 呼び出し元がProviderではない場合、エラーがスローされること
          ✔ Admin権限でない署名の場合、エラーがスローされること
    addRoleByIssuer()
      正常系
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddValidatorAccountIdIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkAddValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"address","name":"validatorEoa","type":"address"}],"name":"checkAddValidatorRoleIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkModAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkModValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"}],"name":"checkSyncAccountStatusIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkValidatorAndAccountExist","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountIdExistenceByValidatorId","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountWithLimits","outputs":[{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"}],"internalType":"struct AccountDataWithLimitData","name":"accountData","type":"tuple"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"}],"name":"getValidatorAccountAllList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct BusinessZoneAccountDataWithZoneId[]","name":"businessZoneAccounts","type":"tuple[]"}],"internalType":"struct AccountDataAll","name":"accountDataAll","type":"tuple"}],"internalType":"struct ValidatorAccountsDataALL[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"string","name":"sortOrder","type":"string"}],"name":"getValidatorAccountList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct ValidatorAccountsData[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorAll","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bool","name":"validatorIdExistence","type":"bool"},{"internalType":"bool","name":"issuerIdLinkedFlag","type":"bool"},{"internalType":"address","name":"validatorEoa","type":"address"},{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bool","name":"accountIdExistenceByValidatorId","type":"bool"}],"internalType":"struct ValidatorAccountsExistence[]","name":"validAccountExistence","type":"tuple[]"}],"internalType":"struct ValidatorAll","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorData","outputs":[{"components":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32[]","name":"accountIds","type":"bytes32[]"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"}],"internalType":"struct ValidatorData","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorIdByIndex","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorIdExistence","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
        Issuerのコントラクトアドレスにaccounts[7]のアドレスが設定されている状態
          ✔ 権限が付与されること
        accounts[5]に権限が付与されている状態
          ✔ 同一アカウントに同一権限を付与した場合、RoleGrantedイベントが発火されないこと
      準正常系
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddValidatorAccountIdIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkAddValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"address","name":"validatorEoa","type":"address"}],"name":"checkAddValidatorRoleIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkModAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkModValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"}],"name":"checkSyncAccountStatusIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkValidatorAndAccountExist","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountIdExistenceByValidatorId","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountWithLimits","outputs":[{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"}],"internalType":"struct AccountDataWithLimitData","name":"accountData","type":"tuple"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"}],"name":"getValidatorAccountAllList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct BusinessZoneAccountDataWithZoneId[]","name":"businessZoneAccounts","type":"tuple[]"}],"internalType":"struct AccountDataAll","name":"accountDataAll","type":"tuple"}],"internalType":"struct ValidatorAccountsDataALL[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"string","name":"sortOrder","type":"string"}],"name":"getValidatorAccountList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct ValidatorAccountsData[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorAll","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bool","name":"validatorIdExistence","type":"bool"},{"internalType":"bool","name":"issuerIdLinkedFlag","type":"bool"},{"internalType":"address","name":"validatorEoa","type":"address"},{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bool","name":"accountIdExistenceByValidatorId","type":"bool"}],"internalType":"struct ValidatorAccountsExistence[]","name":"validAccountExistence","type":"tuple[]"}],"internalType":"struct ValidatorAll","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorData","outputs":[{"components":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32[]","name":"accountIds","type":"bytes32[]"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"}],"internalType":"struct ValidatorData","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorIdByIndex","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorIdExistence","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
        Issuerのコントラクトアドレスにaccounts[7]のアドレスが設定されている状態
          ✔ 権限にAdmin権限を指定した場合、エラーがスローされること
          ✔ 呼び出し元がIssuerではない場合、エラーがスローされること
          ✔ Admin権限でない署名の場合、エラーがスローされること
    addRoleByValidator()
      正常系
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddValidatorAccountIdIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkAddValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"address","name":"validatorEoa","type":"address"}],"name":"checkAddValidatorRoleIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkModAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkModValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"}],"name":"checkSyncAccountStatusIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkValidatorAndAccountExist","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountIdExistenceByValidatorId","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountWithLimits","outputs":[{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"}],"internalType":"struct AccountDataWithLimitData","name":"accountData","type":"tuple"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"}],"name":"getValidatorAccountAllList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct BusinessZoneAccountDataWithZoneId[]","name":"businessZoneAccounts","type":"tuple[]"}],"internalType":"struct AccountDataAll","name":"accountDataAll","type":"tuple"}],"internalType":"struct ValidatorAccountsDataALL[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"string","name":"sortOrder","type":"string"}],"name":"getValidatorAccountList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct ValidatorAccountsData[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorAll","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bool","name":"validatorIdExistence","type":"bool"},{"internalType":"bool","name":"issuerIdLinkedFlag","type":"bool"},{"internalType":"address","name":"validatorEoa","type":"address"},{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bool","name":"accountIdExistenceByValidatorId","type":"bool"}],"internalType":"struct ValidatorAccountsExistence[]","name":"validAccountExistence","type":"tuple[]"}],"internalType":"struct ValidatorAll","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorData","outputs":[{"components":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32[]","name":"accountIds","type":"bytes32[]"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"}],"internalType":"struct ValidatorData","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorIdByIndex","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorIdExistence","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
        Validatorのコントラクトアドレスにaccounts[7]のアドレスが設定されている状態
          ✔ 権限が付与されること
        accounts[5]に権限が付与されている状態
          ✔ 同一アカウントに同一権限を付与した場合、RoleGrantedイベントが発火されないこと
      準正常系
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddValidatorAccountIdIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkAddValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"address","name":"validatorEoa","type":"address"}],"name":"checkAddValidatorRoleIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkModAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkModValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"}],"name":"checkSyncAccountStatusIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkValidatorAndAccountExist","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountIdExistenceByValidatorId","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountWithLimits","outputs":[{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"}],"internalType":"struct AccountDataWithLimitData","name":"accountData","type":"tuple"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"}],"name":"getValidatorAccountAllList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct BusinessZoneAccountDataWithZoneId[]","name":"businessZoneAccounts","type":"tuple[]"}],"internalType":"struct AccountDataAll","name":"accountDataAll","type":"tuple"}],"internalType":"struct ValidatorAccountsDataALL[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"string","name":"sortOrder","type":"string"}],"name":"getValidatorAccountList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct ValidatorAccountsData[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorAll","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bool","name":"validatorIdExistence","type":"bool"},{"internalType":"bool","name":"issuerIdLinkedFlag","type":"bool"},{"internalType":"address","name":"validatorEoa","type":"address"},{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bool","name":"accountIdExistenceByValidatorId","type":"bool"}],"internalType":"struct ValidatorAccountsExistence[]","name":"validAccountExistence","type":"tuple[]"}],"internalType":"struct ValidatorAll","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorData","outputs":[{"components":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32[]","name":"accountIds","type":"bytes32[]"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"}],"internalType":"struct ValidatorData","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorIdByIndex","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorIdExistence","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
        Validatorのコントラクトアドレスにaccounts[7]のアドレスが設定されている状態
          ✔ 権限にAdmin権限を指定した場合、エラーがスローされること
          ✔ 呼び出し元がValidatorではない場合、エラーがスローされること
          ✔ Admin権限でない署名の場合、エラーがスローされること
    calcRole()
      正常系
        初期状態
          ✔ 権限値の算出結果が取得できること
    checkAdminRole()
      正常系
        アカウントにAdmin権限が付与されている状態
          ✔ Admin権限がある場合、trueが返されること
          ✔ Admin権限でない署名の場合、falseが返されること
          ✔ 署名期限切れの場合、falseが返されること
          ✔ Admin権限でない署名の場合、falseが返されること
          ✔ 署名が無効の場合、falseが返されること
          ✔ 署名の長さが65バイト未満の場合、GS0001エラーが返されること
          ✔ 署名の長さが65バイト超過の場合、GS0001エラーが返されること
      準正常系
        アカウントにAdmin権限が付与されている状態
          ✔ 署名が無効（not signature）の場合、エラーがスローされること
    checkRole()
      正常系
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddValidatorAccountIdIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkAddValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"address","name":"validatorEoa","type":"address"}],"name":"checkAddValidatorRoleIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkModAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkModValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"}],"name":"checkSyncAccountStatusIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkValidatorAndAccountExist","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountIdExistenceByValidatorId","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountWithLimits","outputs":[{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"}],"internalType":"struct AccountDataWithLimitData","name":"accountData","type":"tuple"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"}],"name":"getValidatorAccountAllList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct BusinessZoneAccountDataWithZoneId[]","name":"businessZoneAccounts","type":"tuple[]"}],"internalType":"struct AccountDataAll","name":"accountDataAll","type":"tuple"}],"internalType":"struct ValidatorAccountsDataALL[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"string","name":"sortOrder","type":"string"}],"name":"getValidatorAccountList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct ValidatorAccountsData[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorAll","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bool","name":"validatorIdExistence","type":"bool"},{"internalType":"bool","name":"issuerIdLinkedFlag","type":"bool"},{"internalType":"address","name":"validatorEoa","type":"address"},{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bool","name":"accountIdExistenceByValidatorId","type":"bool"}],"internalType":"struct ValidatorAccountsExistence[]","name":"validAccountExistence","type":"tuple[]"}],"internalType":"struct ValidatorAll","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorData","outputs":[{"components":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32[]","name":"accountIds","type":"bytes32[]"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"}],"internalType":"struct ValidatorData","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorIdByIndex","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorIdExistence","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
        アカウントにProvider権限が付与されている状態
          ✔ 指定した権限がある場合、trueが返されること
          ✔ Provider権限でない署名の場合、falseが返されること
          ✔ 署名期限切れの場合、falseが返されること
          ✔ 署名が無効の場合、falseが返されること
          ✔ 署名の長さが65バイト未満の場合、GS0001エラーが返されること
          ✔ 署名の長さが65バイト超過の場合、GS0001エラーが返されること
          ✔ チェック対象の権限が空（0）の場合、falseが返されること
    checkSigAccount()
      準正常系
        ✔ 署名の長さが65バイト未満の場合、GS0001エラーが返されること
        ✔ 署名の長さが65バイト超過の場合、GS0001エラーが返されること
        ✔ OneTime公開鍵検証でアドレスが一致しない場合、GS0002エラーが返されること
        ✔ Account署名検証でアドレスが一致しない場合、GS0002エラーが返されること
    _checkValidatorSig_deep()
      準正常系
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddValidatorAccountIdIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkAddValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"address","name":"validatorEoa","type":"address"}],"name":"checkAddValidatorRoleIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkModAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkModValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"}],"name":"checkSyncAccountStatusIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkValidatorAndAccountExist","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountIdExistenceByValidatorId","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountWithLimits","outputs":[{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"}],"internalType":"struct AccountDataWithLimitData","name":"accountData","type":"tuple"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"}],"name":"getValidatorAccountAllList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct BusinessZoneAccountDataWithZoneId[]","name":"businessZoneAccounts","type":"tuple[]"}],"internalType":"struct AccountDataAll","name":"accountDataAll","type":"tuple"}],"internalType":"struct ValidatorAccountsDataALL[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"string","name":"sortOrder","type":"string"}],"name":"getValidatorAccountList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct ValidatorAccountsData[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorAll","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bool","name":"validatorIdExistence","type":"bool"},{"internalType":"bool","name":"issuerIdLinkedFlag","type":"bool"},{"internalType":"address","name":"validatorEoa","type":"address"},{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bool","name":"accountIdExistenceByValidatorId","type":"bool"}],"internalType":"struct ValidatorAccountsExistence[]","name":"validAccountExistence","type":"tuple[]"}],"internalType":"struct ValidatorAll","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorData","outputs":[{"components":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32[]","name":"accountIds","type":"bytes32[]"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"}],"internalType":"struct ValidatorData","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorIdByIndex","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorIdExistence","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
        ✔ 署名期限切れの場合、GS0003エラーが返されること
        ✔ validatorIdのアドレスが登録されている場合、正常終了すること
        ✔ validatorIdのアドレスが一致しない場合、GS0002エラーが返されること
    delAdminRole()
      正常系
        アカウントにAdmin権限が付与されている状態
          ✔ Admin権限のないアカウントを指定した場合、RoleRevokedイベントが発火されないこと
          ✔ 権限が削除されること
      準正常系
        アカウントにAdmin権限が付与されている状態
          ✔ 権限を削除するアカウントがDEFAULT_ADMIN権限の場合、エラーがスローされること
          ✔ 呼び出し元がDEFAULT_ADMIN権限ではない場合、エラーがスローされること
    delProviderRole()
      正常系
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddValidatorAccountIdIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkAddValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"address","name":"validatorEoa","type":"address"}],"name":"checkAddValidatorRoleIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkModAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkModValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"}],"name":"checkSyncAccountStatusIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkValidatorAndAccountExist","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountIdExistenceByValidatorId","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountWithLimits","outputs":[{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"}],"internalType":"struct AccountDataWithLimitData","name":"accountData","type":"tuple"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"}],"name":"getValidatorAccountAllList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct BusinessZoneAccountDataWithZoneId[]","name":"businessZoneAccounts","type":"tuple[]"}],"internalType":"struct AccountDataAll","name":"accountDataAll","type":"tuple"}],"internalType":"struct ValidatorAccountsDataALL[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"string","name":"sortOrder","type":"string"}],"name":"getValidatorAccountList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct ValidatorAccountsData[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorAll","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bool","name":"validatorIdExistence","type":"bool"},{"internalType":"bool","name":"issuerIdLinkedFlag","type":"bool"},{"internalType":"address","name":"validatorEoa","type":"address"},{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bool","name":"accountIdExistenceByValidatorId","type":"bool"}],"internalType":"struct ValidatorAccountsExistence[]","name":"validAccountExistence","type":"tuple[]"}],"internalType":"struct ValidatorAll","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorData","outputs":[{"components":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32[]","name":"accountIds","type":"bytes32[]"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"}],"internalType":"struct ValidatorData","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorIdByIndex","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorIdExistence","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
        アカウントにProvider権限が付与されている状態
          ✔ Provider権限のないアカウントを指定した場合、RoleRevokedイベントが発火されないこと
          ✔ 権限が削除されること
      準正常系
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddValidatorAccountIdIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkAddValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"address","name":"validatorEoa","type":"address"}],"name":"checkAddValidatorRoleIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkModAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkModValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"}],"name":"checkSyncAccountStatusIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkValidatorAndAccountExist","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountIdExistenceByValidatorId","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountWithLimits","outputs":[{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"}],"internalType":"struct AccountDataWithLimitData","name":"accountData","type":"tuple"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"}],"name":"getValidatorAccountAllList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct BusinessZoneAccountDataWithZoneId[]","name":"businessZoneAccounts","type":"tuple[]"}],"internalType":"struct AccountDataAll","name":"accountDataAll","type":"tuple"}],"internalType":"struct ValidatorAccountsDataALL[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"string","name":"sortOrder","type":"string"}],"name":"getValidatorAccountList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct ValidatorAccountsData[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorAll","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bool","name":"validatorIdExistence","type":"bool"},{"internalType":"bool","name":"issuerIdLinkedFlag","type":"bool"},{"internalType":"address","name":"validatorEoa","type":"address"},{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bool","name":"accountIdExistenceByValidatorId","type":"bool"}],"internalType":"struct ValidatorAccountsExistence[]","name":"validAccountExistence","type":"tuple[]"}],"internalType":"struct ValidatorAll","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorData","outputs":[{"components":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32[]","name":"accountIds","type":"bytes32[]"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"}],"internalType":"struct ValidatorData","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorIdByIndex","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorIdExistence","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
        アカウントにProvider権限が付与されている状態
          ✔ 呼び出し元がDEFAULT_ADMIN権限ではない場合、エラーがスローされること
    delIssuerRole()
      正常系
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddValidatorAccountIdIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkAddValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"address","name":"validatorEoa","type":"address"}],"name":"checkAddValidatorRoleIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkModAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkModValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"}],"name":"checkSyncAccountStatusIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkValidatorAndAccountExist","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountIdExistenceByValidatorId","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountWithLimits","outputs":[{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"}],"internalType":"struct AccountDataWithLimitData","name":"accountData","type":"tuple"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"}],"name":"getValidatorAccountAllList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct BusinessZoneAccountDataWithZoneId[]","name":"businessZoneAccounts","type":"tuple[]"}],"internalType":"struct AccountDataAll","name":"accountDataAll","type":"tuple"}],"internalType":"struct ValidatorAccountsDataALL[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"string","name":"sortOrder","type":"string"}],"name":"getValidatorAccountList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct ValidatorAccountsData[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorAll","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bool","name":"validatorIdExistence","type":"bool"},{"internalType":"bool","name":"issuerIdLinkedFlag","type":"bool"},{"internalType":"address","name":"validatorEoa","type":"address"},{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bool","name":"accountIdExistenceByValidatorId","type":"bool"}],"internalType":"struct ValidatorAccountsExistence[]","name":"validAccountExistence","type":"tuple[]"}],"internalType":"struct ValidatorAll","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorData","outputs":[{"components":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32[]","name":"accountIds","type":"bytes32[]"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"}],"internalType":"struct ValidatorData","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorIdByIndex","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorIdExistence","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
        アカウントにIssuer権限が付与されている状態
          ✔ Issuer権限のないアカウントを指定した場合、RoleRevokedイベントが発火されないこと
          ✔ 権限が削除されること
      準正常系
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddValidatorAccountIdIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkAddValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"address","name":"validatorEoa","type":"address"}],"name":"checkAddValidatorRoleIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkModAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkModValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"}],"name":"checkSyncAccountStatusIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkValidatorAndAccountExist","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountIdExistenceByValidatorId","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountWithLimits","outputs":[{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"}],"internalType":"struct AccountDataWithLimitData","name":"accountData","type":"tuple"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"}],"name":"getValidatorAccountAllList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct BusinessZoneAccountDataWithZoneId[]","name":"businessZoneAccounts","type":"tuple[]"}],"internalType":"struct AccountDataAll","name":"accountDataAll","type":"tuple"}],"internalType":"struct ValidatorAccountsDataALL[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"string","name":"sortOrder","type":"string"}],"name":"getValidatorAccountList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct ValidatorAccountsData[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorAll","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bool","name":"validatorIdExistence","type":"bool"},{"internalType":"bool","name":"issuerIdLinkedFlag","type":"bool"},{"internalType":"address","name":"validatorEoa","type":"address"},{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bool","name":"accountIdExistenceByValidatorId","type":"bool"}],"internalType":"struct ValidatorAccountsExistence[]","name":"validAccountExistence","type":"tuple[]"}],"internalType":"struct ValidatorAll","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorData","outputs":[{"components":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32[]","name":"accountIds","type":"bytes32[]"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"}],"internalType":"struct ValidatorData","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorIdByIndex","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorIdExistence","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
        アカウントにIssuer権限が付与されている状態
          ✔ 呼び出し元がDEFAULT_ADMIN権限ではない場合、エラーがスローされること
    delValidatorRole()
      正常系
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddValidatorAccountIdIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkAddValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"address","name":"validatorEoa","type":"address"}],"name":"checkAddValidatorRoleIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkModAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkModValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"}],"name":"checkSyncAccountStatusIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkValidatorAndAccountExist","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountIdExistenceByValidatorId","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountWithLimits","outputs":[{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"}],"internalType":"struct AccountDataWithLimitData","name":"accountData","type":"tuple"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"}],"name":"getValidatorAccountAllList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct BusinessZoneAccountDataWithZoneId[]","name":"businessZoneAccounts","type":"tuple[]"}],"internalType":"struct AccountDataAll","name":"accountDataAll","type":"tuple"}],"internalType":"struct ValidatorAccountsDataALL[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"string","name":"sortOrder","type":"string"}],"name":"getValidatorAccountList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct ValidatorAccountsData[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorAll","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bool","name":"validatorIdExistence","type":"bool"},{"internalType":"bool","name":"issuerIdLinkedFlag","type":"bool"},{"internalType":"address","name":"validatorEoa","type":"address"},{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bool","name":"accountIdExistenceByValidatorId","type":"bool"}],"internalType":"struct ValidatorAccountsExistence[]","name":"validAccountExistence","type":"tuple[]"}],"internalType":"struct ValidatorAll","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorData","outputs":[{"components":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32[]","name":"accountIds","type":"bytes32[]"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"}],"internalType":"struct ValidatorData","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorIdByIndex","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorIdExistence","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
        アカウントにValidator権限が付与されている状態
          ✔ Validator権限のないアカウントを指定した場合、RoleRevokedイベントが発火されないこと
          ✔ 権限が削除されること
      準正常系
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAddValidatorAccountIdIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkAddValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"address","name":"validatorEoa","type":"address"}],"name":"checkAddValidatorRoleIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkModAccountIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"checkModValidatorIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"}],"name":"checkSyncAccountStatusIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkValidatorAndAccountExist","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountIdExistenceByValidatorId","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getAccountWithLimits","outputs":[{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"}],"internalType":"struct AccountDataWithLimitData","name":"accountData","type":"tuple"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"}],"name":"getValidatorAccountAllList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"},{"internalType":"uint256","name":"mintLimit","type":"uint256"},{"internalType":"uint256","name":"burnLimit","type":"uint256"},{"internalType":"uint256","name":"chargeLimit","type":"uint256"},{"internalType":"uint256","name":"dischargeLimit","type":"uint256"},{"internalType":"uint256","name":"transferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDate","type":"uint256"},{"components":[{"internalType":"uint256","name":"cumulativeMintLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeMintAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeBurnAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeChargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeDischargeAmount","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferLimit","type":"uint256"},{"internalType":"uint256","name":"cumulativeTransferAmount","type":"uint256"}],"internalType":"struct CumulativeTransactionLimits","name":"cumulativeTransactionLimits","type":"tuple"},{"components":[{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct BusinessZoneAccountDataWithZoneId[]","name":"businessZoneAccounts","type":"tuple[]"}],"internalType":"struct AccountDataAll","name":"accountDataAll","type":"tuple"}],"internalType":"struct ValidatorAccountsDataALL[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"string","name":"sortOrder","type":"string"}],"name":"getValidatorAccountList","outputs":[{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"string","name":"accountName","type":"string"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"bytes32","name":"accountStatus","type":"bytes32"},{"internalType":"bytes32","name":"reasonCode","type":"bytes32"},{"internalType":"uint256","name":"appliedAt","type":"uint256"},{"internalType":"uint256","name":"registeredAt","type":"uint256"},{"internalType":"uint256","name":"terminatingAt","type":"uint256"},{"internalType":"uint256","name":"terminatedAt","type":"uint256"}],"internalType":"struct ValidatorAccountsData[]","name":"accounts","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorAll","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bool","name":"validatorIdExistence","type":"bool"},{"internalType":"bool","name":"issuerIdLinkedFlag","type":"bool"},{"internalType":"address","name":"validatorEoa","type":"address"},{"components":[{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"bool","name":"accountIdExistenceByValidatorId","type":"bool"}],"internalType":"struct ValidatorAccountsExistence[]","name":"validAccountExistence","type":"tuple[]"}],"internalType":"struct ValidatorAll","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorData","outputs":[{"components":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32[]","name":"accountIds","type":"bytes32[]"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"bytes32","name":"validatorAccountId","type":"bytes32"}],"internalType":"struct ValidatorData","name":"","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getValidatorIdByIndex","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"getValidatorIdExistence","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
        アカウントにValidator権限が付与されている状態
          ✔ 呼び出し元がDEFAULT_ADMIN権限ではない場合、エラーがスローされること

  Account
    version()
      正常系
        ✔ versionが取得する
    initialize()
      正常系
        ✔ should revert when initialized
    addAccount()
      準正常系
        初期状態
          ✔ 呼び出し元がValidatorではない場合、エラーがスローされること
        accountが登録されている状態
          ✔ should revert when accountId is already registered and called from fake validator
          ✔ should revert when accountId is 0x00 is  and called from fake validator
    modAccount()
      準正常系
        Caller is not validator
          ✔ 呼び出し元がValidatorではない場合、エラーがスローされること
    addAccountRole()
      準正常系
        初期状態
          ✔ 呼び出し元がIssuerではない場合、エラーがスローされること
    setAccountStatus()
      準正常系
        初期状態
          ✔ 呼び出し元がIssuerではない場合、エラーがスローされること
        fake call from issuer
          ✔ should revert when accountId is not registered and call from fake issuer
    setTerminated()
      準正常系
        初期状態
          ✔ 呼び出し元がValidatorではない場合、エラーがスローされること
        fake call from validator to setTerminated
          ✔ should revert when accountId is not registered and call from fake validator
          ✔ should revert when accountId is not valid and call from fake validator
    addZone()
      準正常系
        初期状態
          ✔ should revert when account is not registered
    approve()
      正常系
        accountが登録されている状態
          ✔ allowanceが設定されること
        accountにallowanceが設定されている状態
          ✔ 異なる宛先に対しapproveを行なった場合、異なるallowanceが設定されること
          ✔ 金額を変更しapproveを行なった場合、allowanceが更新されること
    getAccountLimit()
      準正常系
        directly call getAccountLimit when hasValidator false
          ✔ should return zero data and error when hasValidator false
          ✔ should return zero data and error data when invalid validator
    getAllowance()
      準正常系
        初期状態
          ✔ 呼び出し元がValidatorではない場合、エラーがスローされること
        getAllowance fake call from token and financialCheck
          ✔ should return same allowance data as normal when call from token
          ✔ should return zero data and error when owner not exist
    getAllowanceList()
      準正常系
        初期状態
          ✔ 呼び出し元がTokenではない場合、エラーがスローされること
    mint()
      準正常系
        初期状態
          ✔ 呼び出し元がTokenではない場合、エラーがスローされること
    burn()
      準正常系
        初期状態
          ✔ 呼び出し元がTokenではない場合、エラーがスローされること
    calcBalance()
      準正常系
        初期状態
          ✔ 呼び出し元がTokenではない場合、エラーがスローされること
    calcAllowance()
      準正常系
        初期状態
          ✔ 呼び出し元がTokenではない場合、エラーがスローされること
    editBalance()
      準正常系
        初期状態
          ✔ 呼び出し元がTokenではない場合、エラーがスローされること
          ✔ calcPattern is not 2 or 3, this function should return and pass test
    hasAccount()
      正常系
        accountが登録されている状態
          ✔ アカウント存在がチェックする
          ✔ 存在しないアカウントがチェックする場合エラー
          ✔ 空アカウントがチェックする場合エラー
    getAccount()
      正常系
        accountが登録されている状態
          ✔ Account情報が取得する
          ✔ 存在しないアカウントが取得する場合エラー
    getAccountCount()
      正常系
        accountが登録されている状態
          ✔ アカウント数が取得する
    getDestinationAccount()
      準正常系
        初期状態
          ✔ 呼び出し元がValidatorではない場合、エラーがスローされること
    getAccountAll()
      準正常系
        初期状態
          ✔ 呼び出し元がValidatorではない場合、エラーがスローされること
    getAccountId()
      正常系
        accountが登録されている状態
          ✔ 0を指定すると1番目のアカウントが取得する
          ✔ 範囲外インデックスが指定する場合エラー
          ✔ 2番目のアカウントが取得する
    getZoneByAccountId()
      正常系
        provider, providerRole, issuer, validator, token, account, 二つのbusinessZoneAccountが登録されている状態
          ✔ zone情報のリストが取得できること
    isFrozen()
      準正常系
        accountが登録されている状態
          ✔ should return error when account is not registered
    isTerminated()
      正常系
        accountが登録されている状態
          ✔ 解約確認を行う
          ✔ 存在しないアカウントの場合はエラー
    balanceOf()
      正常系
        初期状態
          ✔ 呼び出し元がTokenではない場合、エラーが返されること
      準正常系
        account is not registered
          ✔ should revert when accountId is not registered
          ✔ should revert when accountId is invalid and called from fake token
    forceBurn()
      正常系
        issuerRole, accountが登録されている状態
          ✔ Accountの残高を強制償却できること,totalSupplyが減算されること (52ms)
      準正常系
        issuerRole, accountが登録されている状態
          ✔ Issuerコントラクト以外から呼び出した場合、エラーがスローされること
          ✔ accountが存在しない場合、エラーがスローされること
          ✔ Accountが凍結状態でない場合、エラーがスローされること
          ✔ 空accountIdを指定した場合、エラーがスローされること
        account is not registered or frozen
          ✔ should revert accounId not exist when check hasAccount in isFrozen
          ✔ should revert accounId not valid when check hasAccount in isFrozen
          ✔ should revert account not frozen when check isFrozen in forceBurn
    partialForceBurn()
      正常系
        fin Account残高が300で、biz Account残高が150の場合
          ✔ fin Account残高が指定したburnedAmountより大きい場合、fin Account残高のみが更新されること,totalSupplyが減算されること
          ✔ fin Account残高が指定したburnedAmountより小さく、total残高が大きい場合、biz Account残高が全て消却されること,totalSupplyが減算されること
          ✔ partialForceBurn実行後にAfterBalanceイベントが複数ゾーンで正しく発行されること
      準正常系
        issuerRole, accountが登録されている状態
          ✔ Issuerコントラクト以外から呼び出した場合、エラーがスローされること
          ✔ accountが存在しない場合、エラーがスローされること
          ✔ 空accountIdを指定した場合、エラーがスローされること
          ✔ burnedAmountが残高より大きい場合、ACCOUNT_INVALID_BURNED_AMOUNTエラーがスローされること
          ✔ burnedBalanceが実際の残高と一致しない場合、ACCOUNT_INVALID_BURNED_BALANCEエラーがスローされること
          ✔ Accountが凍結状態でない場合、エラーがスローされること
    emitAfterBalance()
      正常系
        登録済みアカウントの場合
          ✔ 正常にイベントが発行されること
          ✔ 同一アカウントでもイベントが発行されること
        未登録アカウントの場合
          ✔ 存在しないアカウントでもエラーにならずイベントが発行されること
      アクセス制御
        ✔ どのアドレスからでも呼び出し可能であること
      イベントデータの検証
        ✔ イベントにtraceIdが正しく設定されること
        ✔ 複数のBizZoneを持つアカウントでFinZone→BizZone（ZoneID昇順）の順に残高が返されること
      再入可能性の確認
        ✔ 同じトランザクション内で複数回呼び出し可能であること

  AccountSyncBridge
    initialize()
      準正常系
        初期状態
          ✔ IBCHandlerのコントラクトアドレスが空の場合、エラーをスローすること
          ✔ Providerコントラクトアドレスが空の場合、エラーをスローすること
          ✔ Validatorコントラクトアドレスが空の場合、エラーをスローすること
          ✔ AccessCtrlコントラクトアドレスが空の場合、エラーをスローすること
          ✔ BusinessZoneAccountコントラクトアドレスが空の場合、エラーをスローすること
          ✔ IBCTokenコントラクトアドレスが空の場合、エラーをスローすること
          ✔ should revert when initialized
    version()
      正常系
        初期状態
          ✔ バージョン情報を取得できること
    setAddress()
      正常系
        ✔ _validator, _accessCtrl, _businessZoneAccount can be set normally by admin
      準正常系
        ✔ should revert when not admin
    getConfig()
      正常系
        初期状態
          ✔ version情報を取得できること
    getPort()
      正常系
        ✔ accountSyncSourcePort is return normally
    syncAccount
      正常系
        初期状態
          ✔ BizZoneアカウント申し込みを実行した場合、BizZoneのアカウント状態が更新されていること
          ✔ BizZoneアカウント申し込みを実行した場合、BizZoneのアカウント状態が更新されていること
          ✔ BizZoneアカウント解約申し込みを実行した場合、BizZoneのアカウント状態が更新されていること
      準正常系
        初期状態
          ✔ 不正なAccountStatusが設定された場合、エラーがスローされること
          ✔ should revert when fin zone op not allowed
    onRecvPacket
      正常系
        初期状態
          ✔ BizZoneからpacketを受け取ることができた場合、FinZone管理のBizZoneアカウントが更新されること
      準正常系
        初期状態
          ✔ packet空の場合、エラーがスローされること
          ✔ should revert when packet is from fin zone
    onAcknowledgementPacket
      正常系
        初期状態
          ✔ FinZoneからpacketを受け取ることができた場合、BizZone管理のFinZoneアカウントがリセットされないこと
          ✔ should revert when caller is not ibc
    onChanOpenInit()
      正常系
        ✔ accountSyncSourceVersion return normally
        ✔ accountSyncSourceVersion return normally when _msg.version is empty
      準正常系
        ✔ should revert when caller is not ibc
        ✔ should revert when version is mismatch
    onChanOpenTry()
      正常系
        ✔ accountSyncSourceVersion return normally
      準正常系
        ✔ should revert when caller is not ibc
        ✔ should revert when msg_.counterpartyVersion is mismatch
    onTimeoutPacket
      正常系
        初期状態
          ✔ FinZoneからpacketを受け取ることができた場合、BizZoneのアカウントがリセットされないこと
    recoverPacket
      正常系
        初期状態
          ✔ BizZoneからpacketを受け取ることができた場合、FinZone管理のBizZoneアカウントが更新されること
      準正常系
        初期状態
          ✔ ADMIN権限ではない場合、エラーが返却されること
          ✔ 署名が無効の場合、エラーがスローされること
          ✔ 署名が無効（not signature）の場合、エラーがスローされること

  BalanceSyncBridge
    initialize()
      準正常系
        初期状態
          ✔ IBCHandlerのコントラクトアドレスが空の場合、エラーをスローすること
          ✔ IBCTokenコントラクトアドレスが空の場合、エラーをスローすること
          ✔ Accountコントラクトアドレスが空の場合、エラーをスローすること
          ✔ AccessCtrlコントラクトアドレスが空の場合、エラーをスローすること
          ✔ should revert if the contract is already initialized
    version()
      正常系
        初期状態
          ✔ バージョン情報を取得できること
    ibcAddress()
      正常系
        初期状態
          ✔ ibcAddress return the correct address
    setAddress()
      正常系
        初期状態
          ✔ _ibcToken, _account, _accessCtrl can be set normally
        準正常系
          ✔ should revert when not admin
    getConfig()
      正常系
        初期状態
          ✔ getConfig return the correct config of BalanceSyncBridge
    syncTransfer
      正常系
        初期状態
          ✔ BizZoneからFinZoneへの残高同期が成功した場合、BizZoneの残高がリセットされていないこと
    onRecvPacket
      正常系
        初期状態
          ✔ BizZoneからpacketを受け取ることができた場合、FinZone管理のBizZoneアカウント残高が更新されること
      準正常系
        初期状態
          ✔ packet空の場合、エラーがスローされること
    onChanOpenInit()
      正常系
        ✔ balanceSyncSourceVersion return normally
        ✔ balanceSyncSourceVersion return normally when _msg.version is empty
      準正常系
        ✔ should revert when caller is not ibc
        ✔ should revert when version is mismatch
    onChanOpenTry()
      正常系
        ✔ balanceSyncSourceVersion return normally
      準正常系
        ✔ should revert when caller is not ibc
        ✔ should revert when msg_.counterpartyVersion is mismatch
    recoverPacket
      正常系
        初期状態
          ✔ BizZoneからpacketを受け取ることができた場合、FinZone管理のBizZoneアカウントが更新されること
      準正常系
        初期状態
          ✔ ADMIN権限ではない場合、エラーが返却されること
          ✔ 署名が無効の場合、エラーがスローされること
          ✔ 署名が無効（not signature）の場合、エラーがスローされること

  BusinessZoneAccount
    version()
      正常系
        ✔ versionが取得する
    initialize()
      正常系
        ✔ should revert when initialized
    setActiveBusinessAccountWithZone()
      正常系
        account, ibcAppAddressが登録されている状態
          初期状態
            ✔ 呼び出し元がValidatorではない場合、エラーがスローされること
            ✔ 呼び出し元がValidatorの場合、イベントが発行され、アクティブに更新されていること
      準正常系
        初期状態
          ✔ should revert when caller is not validator contract
        invalid params test
          ✔ should revert when zoneId is empty
          ✔ should revert when accountId is not registered
    setBizZoneTerminated()
      準正常系
        初期状態
          ✔ should revert when caller is not validator contract
    hasAccountByZone()
      正常系
        初期状態
          ✔ zoneIdが不正である場合、エラーがスローされること
          ✔ accountIdが不正である場合、エラーがスローされること
    isActivatedByZone()
      正常系
        初期状態
          ✔ zoneIdが不正である場合、エラーがスローされること
          ✔ accountIdが不正である場合、エラーがスローされること
        provider, providerRole, issuer, validator, token, account, 1つのbusinessZoneAccountが解約申込となっている状態
          ✔ アカウントがアクティブでない場合、エラーがスローされること
    syncBusinessZoneStatus()
      正常系
        初期状態
          ✔ 呼び出し元がIBCTokenではない場合、エラーがスローされること
    syncBusinessZoneBalance()
      正常系
        account, ibcAppAddressが登録されている状態
          ✔ 呼び出し元がIBCの場合、エラーがスローされないこと
          ✔ 呼び出し元がIBCの場合、イベントが発行されていること
          ✔ syncBusinessZoneBalance実行後にAfterBalanceイベントが複数ゾーンで正しく発行されること
      準正常系
        初期状態
          ✔ 呼び出し元がTokenではない場合、エラーがスローされること
        Fake call from token contract
          ✔ should pass when fake call from token contract
    addBusinessZoneBalance()
      準正常系
        初期状態
          ✔ 呼び出し元がTokenではない場合、エラーがスローされること
    subtractBusinessZoneBalance()
      準正常系
        初期状態
          ✔ 呼び出し元がTokenではない場合、エラーがスローされること
        Fake call from token contract
          ✔ should pass require token contract but revert with not valid zoneId
    balanceUpdateByRedeemVoucher()
      正常系
        初期状態
          ✔ 呼び出し元がTokenではない場合、エラーがスローされること
        balance should be update when function success
          ✔ redeem voucher success from token contract
      準正常系
        invalid data in account
          ✔ should revert when account balance is not enough
    balanceUpdateByIssueVoucher()
      正常系
        初期状態
          ✔ 呼び出し元がTokenではない場合、エラーがスローされること
        balance update with event emited when issue voucher
          ✔ redeem voucher success from ibcToken contract
          ✔ redeem voucher success from token contract
    forceBurnAllBalance()
      正常系
        issuerRole, accountが登録されている状態
          ✔ BizZoneAccountの残高を強制償却できること
      準正常系
        issuerRole, accountが登録されている状態
          ✔ Accountコントラクト以外から呼び出された場合、エラーがスローされること

  ContractManager
    version()
      version()
        正常系
          ✔ versionが取得できること
    setContracts()
      正常系
        ✔ 任意の値でそれぞれのコントラクトアドレスが設定されること
      準正常系
        ✔ 引数に指定するAccessCtrlのアドレスがAccessCtrlのコントラクトアドレスでない場合、エラーがスローされること
        ✔ Admin権限ではない署名の場合、エラーがスローされること
        ✔ 署名期限切れの場合、エラーがスローされること
        ✔ 署名無効の場合、エラーがスローされること
    setIbcApp()
      正常系
        ✔ 任意のアドレスでibcのコントラクトアドレスが設定されること
      準正常系
        ✔ Admin権限ではない署名の場合、エラーがスローされること
        ✔ 署名期限切れの場合、エラーがスローされること
        ✔ 署名無効の場合、エラーがスローされること
    accessCtrl()
      正常系
        ✔ accessCtrlインスタンスのアドレスが取得できること
    provider()
      正常系
        ✔ providerインスタンスのアドレスが取得できること
    account()
      正常系
        ✔ accountインスタンスのアドレスが取得できること
    validator()
      正常系
        ✔ validatorインスタンスのアドレスが取得できること
    issuer()
      正常系
        ✔ issuerインスタンスのアドレスが取得できること
    token()
      正常系
        ✔ tokenインスタンスのアドレスが取得できること
    transferProxy()
      正常系
        ✔ transferProxyインスタンスのアドレスが取得できること
    balanceSyncBridge()
      正常系
        ✔ balanceSyncBridgeインスタンスのアドレスが取得できること
    ibcApp()
      正常系
        ✔ ibcAppインスタンスのアドレスが取得できること

  FinancialCheck
    version()
      正常系
        ✔ versionが返されること
    initialize()
      正常系
        ✔ should revert when initialized
    checkTransaction()
      正常系
        アカウントが登録されている状態
          ✔ 移転可能かどうか確認できること
          ✔ sendAccountIdとfromAccountIdが異なる場合に移転可能かどうか確認できること
          ✔ sendAccountIdとfromAccountIdが異なり、accountSignatureとsigInfoが空であった場合にアカウント署名がスキップされ処理が成功すること
        Bizアカウントが登録されている状態
          ✔ Bizからのリクエストで移転可能かどうか確認できること
          ✔ BizZoneからのリクエストでaccountSignatureとsigInfoが空であった場合に、アカウント署名がスキップされ処理が成功すること
      準正常系
        アカウントが登録されている状態
          ✔ fromAccountと同じtoAccountを指定した場合、エラーが返却されること
          ✔ fromAccountが存在しない場合、エラーが返却されること
          ✔ fromAccountのステータスが"active"以外である場合、エラーが返却されること
          ✔ sendAccountが存在しない場合、エラーが返却されること
          ✔ sendAccountのステータスが"active"以外である場合、エラーが返却されること
          ✔ toAccountが存在しない場合、エラーが返却されること
          ✔ toAccountのステータスが"active"以外である場合、エラーが返却されること
          ✔ 日次の累積限度額を超えている場合、エラーが返却されること
          ✔ 移転限度額を超えている場合、エラーが返却されること
          ✔ 日次の移転累積限度額を超えている場合、エラーが返却されること
          ✔ 日付が変わった場合、移転累積限度額を超えない金額では正常に処理されること
          ✔ 日付が変わった場合、移転累積限度額を超える金額ではエラーが返却されること
          ✔ アカウント署名が無効である場合、エラーが返却されること
          ✔ 空のアカウント署名が無効である場合、エラーが返却されること
        Bizアカウントが登録されている状態
          ✔ fromAccountがBiz側に存在しない場合、エラーが返却されること
          ✔ fromAccountのBiz側のステータスが"active"以外である場合、エラーが返却されること
          ✔ sendAccountがBiz側に存在しない場合、エラーが返却されること
          ✔ sendAccountのBiz側のステータスが"active"以外である場合、エラーが返却されること
          ✔ toAccountがBiz側に存在しない場合、エラーが返却されること
          ✔ toAccountのBiz側のステータスが"active"以外である場合、エラーが返却されること
        account balance and status check
          ✔ miscValue2が4096よりも大きい場合、エラーが返却されること
          ✔ should return false and error message when balance is not enough in Financial zone
          ✔ should return false and error message when balance is not enough in other zone
          ✔ should return false and error message when fromAccount is disable
          ✔ should return false and error message when fromAccount is not exist
          ✔ should return false and error message when sendAccount is invalid value
          ✔ should return false and error message when fromAccount is invalid value
          ✔ should return false and error message when toAccount is invalid value
      準正常系
        from and to account issuers are different
          ✔ should return false and error message when from and to account issuers are different
    checkExchange()
      正常系
        アカウントが登録されている状態
          ✔ チャージ可能かどうか確認できること
          ✔ Bizからのチャージ可能かどうか確認できること
      準正常系
        アカウントが登録されている状態
          ✔ fromZoneIdとtoZoneIdが同一である場合、エラーが返却されること
          ✔ 限度額を超えている場合、エラーが返却されること
        account is not registered
          ✔ should return false and err message when accountId is not registered
          ✔ should return false and err message when accountId is not valid
        account balance and status check
          ✔ should return false and err message when accountId is not enough balance on Financial Zone
          ✔ should return false and err message when accountId is disabled on Financial Zone
          ✔ should return false and err message when accountId is disabled on toZoneId on other Zone 
    checkSyncAccount()
      正常系
        アカウントが登録されている状態
          ✔ アカウント申込可能かどうか確認できること
          ✔ 強制償却済みのアカウント申込可能かどうか確認できること
      準正常系
        アカウントが登録されている状態
          ✔ 認可しているイシュアに紐づかないアカウントの場合、エラーが返却されること
          ✔ バリデータが未入力の場合、エラーが返却されること
          ✔ アカウントが未入力の場合、エラーが返却されること
          ✔ 存在しないアカウントの場合、エラーが返却されること
          ✔ 開設申込時、アカウントが存在する場合、エラーが返却されること
          ✔ 解約申込時、存在しないアカウントを指定した場合にエラーが返却されること
          ✔ アカウント署名が無効である場合、エラーが返却されること
          ✔ 空のアカウント署名が無効である場合、エラーが返却されること
          ✔ パラメータのaccountStatus が TERMINATING、かつ　Biz ゾーンのアカウント残高がゼロでない場合、エラーが返却されること
        アカウント開設申込されている状態
          ✔ 開設申込済の場合、エラーが返却されること
          ✔ 空validatorIdを指定した場合、エラーが返却されること
        アカウント解約申込されている状態
          ✔ 解約申込済の場合、エラーが返却されること
        アカウント解約されている状態
          ✔ 解約済の場合、エラーが返却されること
        account status is not valid to requirement
          ✔ should run normaly when status from business zone is not terminate
          ✔ should run normaly when input status is not terminate or applying
    checkFinAccountStatus()
      正常系
        アカウントが登録されている状態
          ✔ アカウントのステータスを取得できること
          ✔ アカウントが存在しない場合、取得に失敗すること
          ✔ 無効なアカウントIDを選択した場合、取得に失敗すること
    getAccountLimit()
      正常系
        アカウントが登録されている状態
          ✔ アカウントの限度額情報を取得できること
          ✔ cumulativeDateが昨日以前の場合、累積額が0でデータが返されること
          ✔ アカウントが存在しない場合、取得に失敗すること
          ✔ 無効なアカウントIDを選択した場合、取得に失敗すること
    getBizZoneAccountStatus()
      正常系
        アカウントが登録されている状態
          ✔ Bizアカウントのステータスを取得できること
      準正常系
        アカウントが登録されている状態
          ✔ 存在しないIDの場合、エラーが返却されること
          ✔ ZoneIdが未入力の場合、エラーが返却されること
          ✔ AccountIdが未入力の場合、エラーが返却されること

  getIssuerWithZone()
    正常系
      アカウントが登録されている状態
        ✔ ゾーンに紐付くイシュア情報を取得できること
        ✔ オフセットを指定した際に、ゾーンに紐付くイシュア情報を取得できること
        ✔ オフセットとリミットを指定した際に、ゾーンに紐付くイシュア情報を取得できること
    準正常系
      アカウントが登録されている状態
        ✔ ゾーンに紐付くイシュア情報が存在しない場合、取得に失敗すること
        ✔ limitが取得上限(100件)より大きい場合、エラーが返されること
        ✔ offsetが登録されている件数以上の場合、エラーが返されること

  FinancialZoneAccount
    version()
      正常系
        ✔ versionが取得する
        ✔ versionが取得する
    initialize()
      正常系
        ✔ should revert when initialized
    addAccountLimit()
      正常系
        provider, providerRole, issuer, validator, account, tokenが登録されている状態
          ✔ アカウント限度額追加できること
    modAccountLimit
      準正常系
        limitUpdates is false
          ✔ no data change when limitUpdates is false
    modAccountLimit()
      準正常系
        caller is not valid
          ✔ should revert when caller is not issuer contract
    cumulativeReset()
      準正常系
        caller is not valid
          ✔ should revert when caller is not issuer contract
    cumulativeReset()
      準正常系
        caller is not valid
          ✔ should revert when caller is not issuer contract
          ✔ transferLimitが999,999,999,999を超える場合、エラーがスローされること
          ✔ chargeLimitが999,999,999,999を超える場合、エラーがスローされること
          ✔ mintLimitが999,999,999,999を超える場合、エラーがスローされること
          ✔ burnLimitが999,999,999,999を超える場合、エラーがスローされること
          ✔ cumulativeLimitが999,999,999,999,999を超える場合、エラーがスローされること
          ✔ dischargeLimitが999,999,999,999を超える場合、エラーがスローされること
          ✔ cumulativeMintLimitが999,999,999,999,999を超える場合、エラーがスローされること
          ✔ cumulativeBurnLimitが999,999,999,999,999を超える場合、エラーがスローされること
          ✔ cumulativeChargeLimitが999,999,999,999,999を超える場合、エラーがスローされること
          ✔ cumulativeDischargeLimitが999,999,999,999,999を超える場合、エラーがスローされること
          ✔ cumulativeTransferLimitが999,999,999,999,999を超える場合、エラーがスローされること
    check result after call cumulativeReset()
      準正常系
        check cumulative reset
          ✔ compare result before and after call cumulativeReset
    syncCumulativeReset()
      正常系
        cumulativeAmountが設定されている状態
          ✔ cumulativeAmountが0に更新されること
      準正常系
        共通領域アカウントが登録されていない状態
          ✔ 未登録accountIdを指定した場合、エラーがスローされること
    addCumlativeAmount()
      正常系
        cumulativeAmount run normally
          ✔ should run normally when accountLimitDataMapping[key].cumulativeDate == currentDay
      準正常系
        初期状態
          ✔ 呼び出し元がTokenではない場合、エラーがスローされること
    subtractCumulativeAmount()
      正常系
        subtractCumulativeAmount run normally
          ✔ should run normally and emit event SubtractCumulativeAmount
      準正常系
        初期状態
          ✔ 呼び出し元がTokenではない場合、エラーがスローされること
    syncMint()
      正常系
        cumulativeAmountが0の状態
          ✔ 日付が変わっていない場合、cumulativeAmountが加算されること
          ✔ 日付が変わっていない場合、cumulativeMintAmountが加算されること
          ✔ 日跨りで一日の累積限度額を2回目の発行で超えている場合、cumulativeAmountがリセットされること
          ✔ 日付が変った場合、cumulativeAmountがリセットされた上で加算されること
          ✔ 日付が変った場合、cumulativeMintAmountがリセットされた上で加算されること
          ✔ 日付が変った場合、Mint実行後にcumulativeMintAmountが加算され、cumulativeBurnAmountがリセットされること
    syncBurn()
      正常系
        cumulativeAmountが0の状態
          ✔ 日付が変わっていない場合、cumulativeAmountが加算されること
          ✔ 日付が変わっていない場合、cumulativeBurnAmountが加算されること
          ✔ 日跨りで一日の累積限度額を2回目の償却で超えている場合、cumulativeAmountがリセットされること
          ✔ 日跨りで一日の累積限度額を2回目の償却で超えている場合、cumulativeBurnAmountがリセットされること
          ✔ 日付が変った場合、Burn実行後にcumulativeBurnAmountが加算され、cumulativeMintAmountがリセットされること
    syncCharge()
      正常系
        cumulativeAmountが0の状態
          ✔ 日付が変わっていない場合、cumulativeAmountが加算されること
          ✔ 日付が変わっていない場合、cumulativeChargeAmountが加算されること
          ✔ 日付が変った場合、cumulativeAmountがリセットされた上で加算されること
          ✔ 日付が変った場合、cumulativeChargeAmountがリセットされた上で加算されること
          ✔ 日付が変った場合、Charge実行後にcumulativeChargeAmountが加算され、cumulativeMintAmountとcumulativeBurnAmountがリセットされること
    syncDischarge()
      正常系
        cumulativeAmountが0の状態
          ✔ 日付が変わっていない場合、cumulativeAmountが加算されること
          ✔ 日付が変わっていない場合、cumulativeDischargeAmountが加算されること
          ✔ 日付が変った場合、cumulativeAmountがリセットされた上で加算されること
          ✔ 日付が変った場合、cumulativeDischargeAmountがリセットされた上で加算されること
          ✔ 日付が変った場合、Discharge実行後にcumulativeChargeAmountが加算され、cumulativeMintAmountとcumulativeBurnAmountがリセットされること
    syncTransfer()
      正常系
        cumulativeAmountが0の状態
          ✔ 日付が変わっていない場合、cumulativeAmountが加算されること
          ✔ 日付が変わっていない場合、cumulativeTransferAmountが加算されること
          ✔ 日跨りで一日の累積限度額を5回目の送金で超えている場合、cumulativeAmountがリセットされること
          ✔ 日付が変った場合、cumulativeAmountがリセットされた上で加算されること
          ✔ 日付が変った場合、cumulativeTransferAmountがリセットされた上で加算されること
          ✔ 日付が変った場合、Transfer実行後にcumulativeTransferAmountが加算され、cumulativeMintAmountとcumulativeBurnAmountがリセットされること
    hasAccount()
      準正常系
        account is not valid
          ✔ should return false and error message if accountId == 0x00
          ✔ should return nothing when accountId != 0x00
    getAccountLimitData()
      準正常系
        初期状態
          ✔ 呼び出し元がAccountではない場合、エラーがスローされること
    getJSTDay()
      正常系
        初期状態
          ✔ 本日の0:00に対応するJSTのUNIX秒が返却されること
    convertJSTDay()
      正常系
        初期状態
          ✔ timestampに対応するJSTのUNIX秒が返却されること
    checkCharge()
      準正常系
        account is not valid
          ✔ should return false and error message when account is invalid
          ✔ should return false and error message when account is not registerd
        exceed limit
          ✔ should return false and error when exceed limit
    checkCharge()
      準正常系
        account is not valid
          ✔ should return false and error message when account is invalid
          ✔ should return false and error message when account is not registerd
        exceed limit
          ✔ should return false and error when exceed limit
          ✔ should return error when exceed charge limit
          ✔ should return error when exceed daily charge limit
          ✔ The system should return an error if a charge action exceeds the accumulated limit after the date changes
          ✔ If the date changes, the charge will be processed normally as long as the cumulative charge limit is not exceeded
    checkDischarge()
      準正常系
        account is not valid
          ✔ should return false and error message when account is invalid
          ✔ should return false and error message when account is not registerd
        exceed limit
          ✔ should return false and error when exceed limit
          ✔ should return error when exceed discharge limit
          ✔ should return error when exceed daily discharge limit
          ✔ The system should return an error if a discharge action exceeds the accumulated limit after the date changes
          ✔ If the date changes, the discharge will be processed normally as long as the cumulative discharge limit is not exceeded
    checkTransfer()
      準正常系
        準正常系
          ✔ should return false and error message when account is invalid
          ✔ should return false and error message when account is not registerd
    checkTransfer()
      準正常系
        準正常系
          ✔ should return false and error message when account is invalid
          ✔ should return false and error message when account is not registerd
    checkCumulativeLimit
      準正常系
        exceed limit
          ✔ should return false and error when exceed mint limit
          ✔ should return false and error when exceed daily mint cumulative limit
          ✔ should return false and error when exceed daily limit
          ✔ should return false and error when exceed burn limit
          ✔ should return false and error when exceed daily burn cumulative limit
          ✔ should return false and error when exceed charge limit
          ✔ should return false and error when exceed daily charge cumulative limit
          ✔ should return false and error when exceed discharge limit
          ✔ should return false and error when exceed daily discharge cumulative limit
          ✔ should return false and error when exceed transfer limit
          ✔ should return false and error when exceed daily transfer cumulative limit

  IBCToken
    version()
      正常系
        ✔ versionが返されること
    redeemVoucher()
      正常系
        account, ibcAppAddressが登録されている状態
          ✔ balance, totalSupplyが減算されること
          ✔ 複数ゾーンを持つアカウントのredeemVoucher実行後にAfterBalanceEmittedイベントが正しく発行されること
      準正常系
        account, ibcAppAddressが登録されている状態
          ✔ 呼び出し元が登録したibcAppAddressではない場合、エラーがスローされること
          ✔ balanceが不足している場合、エラーがスローされること
    issueVoucher()
      正常系
        account, ibcAppAddressが登録されている状態
          ✔ balance, totalSupplyが加算されること
          ✔ 複数ゾーンを持つアカウントへのissueVoucher実行後にAfterBalanceEmittedイベントが正しく発行されること
      準正常系
        account, ibcAppAddressが登録されている状態
          ✔ 呼び出し元が登録したibcAppAddressではない場合、エラーがスローされること
          ✔ 解約済みアカウントへのissueVoucherがエラーになること
    transferFromEscrow()
      正常系
        account, ibcAppAddressが登録されている状態
          ✔ miscValueが空でTransferが実行されること
      準正常系
        account, ibcAppAddressが登録されている状態
          ✔ 呼び出し元が登録したibcAppAddressではない場合、エラーがスローされること
          ✔ should revert when zoneId is not valid
    transferToEscrow()
      正常系
        account, ibcAppAddressが登録されている状態
          ✔ miscValueが空, sendAccountIdがfromAccountIdで指定した値でTransferが実行されること
      準正常系
        account, ibcAppAddressが登録されている状態
          ✔ 呼び出し元が登録したibcAppAddressではない場合、エラーがスローされること
          ✔ balanceが不足している場合、エラーがスローされること
    checkAdminRole()
      正常系
        tokenが登録されている状態
          ✔ Admin権限がある場合、trueが返されること
          ✔ Admin権限でない署名の場合、エラーが返されること
          ✔ 署名無効の場合、エラーが返されること
    syncBusinessZoneBalance()
      正常系
        account, ibcAppAddressが登録されている状態
          ✔ 呼び出し元がIBCの場合、エラーがスローされないこと
      準正常系
        account, ibcAppAddressが登録されている状態
          ✔ 呼び出し元がIBCではない場合、エラーがスローされること
    syncBusinessZoneBalance()
      正常系
        account, ibcAppAddressが登録されている状態
          ✔ 呼び出し元がIBCの場合、エラーがスローされないこと
      準正常系
        account, ibcAppAddressが登録されている状態
          ✔ 呼び出し元がIBCではない場合、エラーがスローされること
    initAccountBalance()
      正常系
        account, ibcAppAddressが登録されている状態
          ✔ 呼び出し元がIBCの場合、エラーがスローされないこと
      準正常系
        account, ibcAppAddressが登録されている状態
          ✔ 呼び出し元がIBCではない場合、エラーがスローされること

  Issuer
    version()
      正常系
        ✔ versionが取得できること
    initialize()
      正常系
        ✔ should revert when initialized
    addIssuer()
      正常系
        初期状態
          ✔ issuerが登録されること
          ✔ issuerNameが空文字指定の場合でも、issuerが登録されること
      準正常系
        初期状態
          ✔ Deccuret以外のIssuerが最初に追加されようとした場合、エラーがスローされること
          ✔ Admin権限でない署名の場合、エラーがスローされること
          ✔ 署名期限切れの場合、エラーがスローされること
          ✔ 署名無効の場合、エラーがスローされること
          ✔ 空issuerIdを指定した場合、エラーがスローされること
        issuerが登録されている状態
          ✔ 既存issuerIdを指定した場合、エラーがスローされること
          ✔ 既存bankCodeを指定した場合、エラーがスローされること
    addAccountId()
      準正常系
        初期状態
          ✔ should revert when called directly
        issuerId not valid or exist
          ✔ should revert when issuerId is not valid
          ✔ should revert when issuerId doesn't exist
        accountId doesn't exist
          ✔ should revert when accountId is not exist
    addIssuerRole()
      正常系
        issuerが登録されている状態
          ✔ issuerRoleが追加されること
          ✔ issuerRoleが追加されること（issuerIdが違う）
      準正常系
        issuerが登録されている状態
          ✔ Admin権限でない署名の場合、エラーがスローされること
          ✔ 署名期限切れの場合、エラーがスローされること
          ✔ 署名無効の場合、エラーがスローされること
          ✔ 空EOA指定の場合、エラーがスローされること
          ✔ 未登録issuerId指定の場合、エラーがスローされること
    getAccountList()
      正常系
        issuerRole, accountが登録されている状態
          ✔ 指定したissuerIdに紐づくaccountリストが取得できること (53ms)
          ✔ inAccountIdsに存在しないaccountIdが含まれる場合、空データが取得できること
          ✔ 空のaccountリストでリクエストした場合、空リストが取得できること
          ✔ issuerに紐づくaccountがない場合、エラーがスローされること
      準正常系
        issuer is registered but params invalid
          ✔ should return empty data when limit = 0
          ✔ should return error too large limit when limit > _MAX_LIMIT
          ✔ should return error offset out of index when offset >= inAccountIds.length
    addAccountRole()
      正常系
        issuerRole, accountが登録されている状態
          ✔ roleが追加できること
      準正常系
        accountが登録されている状態
          ✔ issuerRoleがない場合、エラーがスローされること
        issuerRoleが登録されている状態
          ✔ issuerと紐付いていないaccountの場合、エラーがスローされること
          ✔ 空issuerIdを指定した場合、エラーが返されること
          ✔ 空accountIdを指定した場合、エラーが返されること
          ✔ 署名期限切れの場合、エラーがスローされること
          ✔ 署名エラーがスローされること
          ✔ should revert when _hasAccount return false
          ✔ Eoaが空である場合はError
    hasAccount()
      正常系
        accountが登録されている状態
          ✔ issuerに紐づくaccountが存在する場合、trueが取得できること
          ✔ 紐付けられていないaccount指定の場合、エラーが返されること
          ✔ 空accountを指定した場合、エラーが返されること
    modIssuer()
      正常系
        issuerが登録されている状態
          ✔ issuer情報が変更できること、発行者名を指定しない場合、発行者名が元の値のままであること
      準正常系
        issuerが登録されている状態
          ✔ 未登録issuerIdを指定する場合、エラーがスローされること
          ✔ Admin権限がない場合、エラーがスローされること
          ✔ 署名無効の場合、エラーがスローされること
    hasIssuer()
      正常系
        issuerが登録されている状態
          ✔ issuerが存在する場合、trueが取得できること
          ✔ 空issuerIdを指定した場合、エラーが返されること
          ✔ 未登録のissuerId指定の場合、エラーが返されること
    getIssuer()
      正常系
        issuerが登録されている状態
          ✔ issuer情報が取得できること /* DCPF-21196
          ✔ 空issuerNameが取得できること
          ✔ 未登録issuerId指定する場合、エラーが返されること
    getIssuerCount()
      正常系
        issuerが登録されている状態
          ✔ 登録されているissuer数が取得できること
    getIssuerId()
      正常系
        issuerが登録されている状態
          ✔ issuer情報が取得できること
          ✔ 無効状態のissuer情報が取得できること
          ✔ 範囲外のIndex指定の場合、エラーが返されること
    getIssuerList()
      正常系
        issuerが登録されている状態
          ✔ offset0, limit3を指定した場合、1要素目から3件取得できること
          ✔ offset2, limit2を指定した場合、3要素目から2件取得できること
          ✔ offset0, limit10を指定した場合、1要素目から10件取得できること
          ✔ offset1, limit10を指定した場合、2要素目から10件取得できること
          ✔ 最後の1件が取得できること
          ✔ limitが取得上限(100件)以下の場合、issuerリストが取得できること (limit 100)
          ✔ limitが0の場合、空リストが取得できること
          ✔ limitを登録数以上に指定した場合、登録されている要素数の範囲内でのみ取得できること
          ✔ 複数のgetIssuerList呼び出しが同時に行われた場合、それぞれ正しい結果を返すこと /* DCPF-21196
          ✔ limitが取得上限(100件)より大きい場合、エラーが返されること
          ✔ offsetが登録されている件数以上の場合、エラーが返されること /* DCPF-21196
          ✔ offsetが配列の長さと同じ場合、エラーが返されること /* DCPF-21196
      準正常系
        issuerが登録されていない状態
          ✔ issuerが登録されていない場合、空リストが取得できること /* DCPF-21196
    checkRole()
      正常系
        issuer, issuerRoleが登録されている状態
          ✔ roleがある場合、trueが取得できること
          ✔ roleがない場合、falseが取得できること
          ✔ 空issuerIdを指定した場合、エラーが返されること
          ✔ 署名期限切れの場合、エラーが返されること
        issuerが無効の状態
          ✔ roleがある場合、trueが取得できること
    checkMint()
      正常系
        issuer, provider, providerRole, tokenが登録されている状態
          ✔ mintRoleがある場合、trueが取得できること
      準正常系
        issuer, provider, providerRole, tokenが登録されている状態
          ✔ should return error when exceeded mint limit
          ✔ should return error when exceed daily limit
          ✔ should return error when exceed daily mint limit
          ✔ The system should return an error if a minting action exceeds the accumulated limit after the date changes
          ✔ If the date changes, the minting will be processed normally as long as the cumulative minting limit is not exceeded
          ✔ should return error when account is terminated
        issuerRoleが登録されていない状態
          ✔ issuer権限がない場合、エラーをスローすること
        issuerRoleが登録されている状態
          ✔ issuerに紐付けられていないaccountIdを指定した場合、エラーがスローされること
          ✔ 解約されたaccountIdを指定した場合、エラーがスローされること
          ✔ 署名が不正である場合、エラーがスローされること
          ✔ 署名期限切れの場合、エラーがスローされること
    isFrozen()
      正常系
        issuer, provider, providerRole, tokenが登録されている状態
          ✔ should return false and empty err when account is not frozen
          ✔ should return true and empty error when account is frozen
    checkBurn()
      正常系
        issuer, provider, providerRole, tokenが登録されている状態
          ✔ should return true and empty error when checkBurn is normal
      準正常系
        issuer, provider, providerRole, tokenが登録されている状態
          ✔ should return error when exceeded burn limit
          ✔ should return error when exceed daily limit
          ✔ should return error when exceed daily burn limit
          ✔ The system should return an error if a burn action exceeds the accumulated limit after the date changes
          ✔ If the date changes, the minting will be processed normally as long as the cumulative minting limit is not exceeded
          ✔ should return error when account is terminated
          ✔ amountが0の場合、エラーがスローされること
          ✔ 署名が不正である場合、エラーがスローされること
          ✔ 署名期限切れの場合、エラーがスローされること
        issuerRoleが登録されていない状態
          ✔ issuer権限がない場合、エラーをスローすること
    getAccount()
      正常系
        accountが登録されている状態
          ✔ issuerに紐づくaccountの情報が取得できること
          ✔ 解約済みAccountの情報が取得できること
          ✔ 空accountIdを指定した場合、エラーが返されること
          ✔ 未登録Account場合、エラーが返されること
      準正常系
        accountId exist on Issuer but not Account or vice versa
          ✔ should return error and empty data when accountId exist on Account but not Issuer
    modTokenLimit()
      正常系
        issuerRole, accountが登録されている状態
          ✔ Accountの限度額が更新できること
      準正常系
        issuerRole, accountが登録されている状態
          ✔ 空issuerIdを指定した場合、エラーがスローされること
          ✔ 未登録issuerIdを指定した場合、エラーがスローされること
          ✔ 空accountIdを指定した場合、エラーがスローされること
          ✔ 未登録accountIdを指定した場合、エラーがスローされること
          ✔ transferLimitが999,999,999,999を超える場合、エラーがスローされること
          ✔ chargeLimitが999,999,999,999を超える場合、エラーがスローされること
          ✔ mintLimitが999,999,999,999を超える場合、エラーがスローされること
          ✔ burnLimitが999,999,999,999を超える場合、エラーがスローされること
          ✔ dischargeLimitが999,999,999,999を超える場合、エラーがスローされること
          ✔ cumulativeLimitが999,999,999,999,999を超える場合、エラーがスローされること
          ✔ cumulativeMintLimitが999,999,999,999,999を超える場合、エラーがスローされること
          ✔ cumulativeBurnLimitが999,999,999,999,999を超える場合、エラーがスローされること
          ✔ cumulativeChargeLimitが999,999,999,999,999を超える場合、エラーがスローされること
          ✔ cumulativeDisChargeLimitが999,999,999,999,999を超える場合、エラーがスローされること
          ✔ cumulativeTransferLimitが999,999,999,999,999を超える場合、エラーがスローされること
          ✔ 署名が無効の場合、エラーがスローされること
          ✔ issuer権限がない場合、エラーをスローすること
        issuerが有効の状態, accountが解約状態
          ✔ 解約状態のaccountIdを指定した場合、エラーがスローされること
        accountId exist on Issuer but not Account
          ✔ should revert when isTerminated returns error when accountId exist on Issuer but not Account
    cumulativeReset()
      正常系
        issuerRole, accountが登録されている状態
          ✔ Accountの累積限度額が初期化されること
      準正常系
        issuerRoleが登録されている状態, accountが登録されていない状態
          ✔ 未登録accountIdを指定した場合、エラーがスローされること
        accountが登録されている状態
          ✔ 空issuerIdを指定した場合、エラーがスローされること
          ✔ 未登録issuerIdを指定した場合、エラーがスローされること
          ✔ 空accountIdを指定した場合、エラーがスローされること
          ✔ 署名が不正である場合、エラーがスローされること
          ✔ issuer権限がない場合、エラーをスローすること
        issuerが有効の状態, accountが解約状態
          ✔ 解約状態のaccountIdを指定した場合、エラーがスローされること
        accountId exist on Issuer but not Account
          ✔ should revert when isTerminated returns error when accountId exist on Issuer but not Account
    setAccountStatus()
      正常系
        issuerRole, accountが登録されている状態
          ✔ Accountを凍結できること
          ✔ Accountを凍結解除できること
      準正常系
        issuerRole, accountが登録されている状態
          ✔ should revert when issuerId is not valid
          ✔ Accountが存在しない場合、凍結に失敗すること
          ✔ Accountを強制償却済みからアクティブにできること
          ✔ Accountが凍結以外の状態の場合、凍結解除に失敗すること
          ✔ Accountがアクティブ以外の状態の場合、凍結に失敗すること
          ✔ 未登録issuerIdを指定した場合、エラーがスローされること
          ✔ 空accountIdを指定した場合、エラーがスローされること
          ✔ Issuer署名が不正である場合、凍結に失敗すること
          ✔ Issuer署名が不正である場合、凍結解除に失敗すること
          ✔ issuer権限がない場合、エラーをスローすること
    forceBurn()
      正常系
        issuerRole, accountが登録されている状態
          ✔ Accountの残高を強制償却できること
      準正常系
        issuerRole, accountが登録されている状態
          ✔ Accountが存在しない場合、強制償却に失敗すること
          ✔ 未登録issuerIdを指定した場合、エラーがスローされること
          ✔ 空accountIdを指定した場合、エラーがスローされること
          ✔ 署名期限切れの場合、エラーがスローされること
          ✔ 署名が不正である場合、エラーがスローされること
          ✔ issuer権限がない場合、エラーをスローすること
    partialForceBurn()
      正常系
        issuerRole, accountが登録されている状態
          ✔ Accountの残高を部分的に強制償却できること
          burnedAmountがfin Accountより多い場合
            ✔ totalAmountがburnedAmountより多い場合
            ✔ burnedAmountが残高より大きい場合、エラーがスローされること
            ✔ expectedBalanceがburnedBalanceと一致しない場合、エラーがスローされること
      準正常系
        issuerRole, accountが登録されている状態
          ✔ Accountが存在しない場合、部分的強制償却に失敗すること
          ✔ 未登録issuerIdを指定した場合、エラーがスローされること
          ✔ 空accountIdを指定した場合、エラーがスローされること
          ✔ 署名期限切れの場合、エラーがスローされること
          ✔ 署名が不正である場合、エラーがスローされること
          ✔ issuer権限がない場合、エラーをスローすること

  addBizZoneToIssuer()
    正常系
      provider, issuer が登録されている状態
        ✔ issuerが登録されること
    準正常系
      初期状態
        ✔ Admin権限でない署名の場合、エラーがスローされること
        ✔ 署名期限切れの場合、エラーがスローされること
        ✔ 署名無効の場合、エラーがスローされること
      IssuerRoleが登録されている状態
        ✔ 存在しないゾーンを指定した場合、エラーがスローされること
        ✔ 登録済のissuerIdを指定した場合、エラーがスローされること

  deleteBizZoneToIssuer()
    正常系
      provider, issuer が登録されている状態
        ✔ issuerが削除されること
    準正常系
      初期状態
        ✔ Admin権限でない署名の場合、エラーがスローされること
        ✔ 署名期限切れの場合、エラーがスローされること
        ✔ 署名無効の場合、エラーがスローされること
      IssuerRoleが登録されている状態
        ✔ 存在しないゾーンを指定した場合、エラーがスローされること

  JPYTokenTransferBridge Test
    initialize()
      準正常系
        初期状態
          ✔ IBCHandlerのコントラクトアドレスが空の場合。エラーをスローすること
          ✔ IBCTokenのコントラクトアドレスが空の場合。エラーをスローすること
          ✔ AccessCtrlのコントラクトアドレスが空の場合。エラーをスローすること
          ✔ should revert if already initialized
    version()
      正常系
        初期状態
          ✔ version情報を取得できること
    setAddress()
      正常系
        初期状態
          ✔ _ibcToken, _accessCtrl can be set normally
        準正常系
          ✔ should revert when not admin
    getConfig()
      正常系
        初期状態
          ✔ version情報を取得できること
    registerEscrowAccount()
      正常系
        初期状態
          ✔ EscrowAccountが登録できること
      準正常系
        EscrowAccountが登録されている状態
          ✔ 重複してEscrowAccountを登録しようとした場合、エラーがスローされること
    unregisterEscrowAccount()
      正常系
        EscrowAccountが登録されている状態
          ✔ EscrowAccountが削除できること
    escrowAccount()
      正常系
        初期状態
          ✔ EscrowAccountが未登録の場合は0が返ること
        EscrowAccountが登録されている状態
          ✔ EscrowAccountが登録されている場合はIDが返ること
    transfer()
      正常系
        EscrowAccountが登録されている状態
          ✔ (TransferToEscrow)FinZoneでIBCのTransferを行った場合に、EscrowAccountの残高が加算されていること
          ✔ (RedeemVoucher)BizZoneでIBCのTransferを行った場合に、BizZoneアカウントの残高が減算されていること
      準正常系
        ✔ EscrowAccountが登録されていない場合、エラーがスローされること
        初期状態
          ✔ 送金先と送金元に同一の領域が設定された場合、エラーがスローされること
    discharge()
      準正常系
        初期状態
          ✔ 同一リージョン間のディスチャージの場合、エラーがスローされること (FIN → FIN)
          ✔ 同一リージョン間のディスチャージの場合、エラーがスローされること (BIZ → BIZ)
          ✔ FINからBIZへのディスチャージの場合、エラーがスローされること (FIN → BIZ)
      正常系
        EscrowAccountが登録されている状態
          ✔ BIZ → FINのディスチャージが正常に実行されること
    onRecvPacket()
      正常系
        EscrowAccountが登録されていて、残高を保持している状態
          ✔ (TransferFromEscrow)FinZoneにおいてBizZoneからpacketを受信した場合に、EscrowAccountの残高が減算されていること
          ✔ (issueVoucher)BizZoneにおいてFinZoneからpacketを受信した場合に、BizZoneアカウントの残高が加算されていること
      準正常系
        初期状態
          ✔ packet空の場合、エラーがスローされること
          ✔ EscrowAccountが登録されていない場合、エラーがスローされること
    onAcknowledgementPacket()
      正常系
        EscrowAccountが登録されていて、EscrowAccountが残高を保持している状態
          ✔ FinZoneからBizZoneへの送金が成功した場合、EscrowAccountからFinZoneアカウントに返金されないこと
    recoverPacket
      正常系
        EscrowAccountが登録されていて、残高を保持している状態
          ✔ (TransferFromEscrow)FinZoneにおいてBizZoneからpacketを受信した場合に、EscrowAccountの残高が減算されていること
      準正常系
        初期状態
          ✔ ADMIN権限ではない場合、エラーが返却されること
          ✔ 署名が無効の場合、エラーがスローされること
          ✔ 署名が無効（not signature）の場合、エラーがスローされること
    onChanOpenInit()
      正常系
        ✔ tokenTransferSourceVersion return normally
        ✔ tokenTransferSourceVersion return normally when _msg.version is empty
      準正常系
        ✔ should revert when caller is not ibc
        ✔ should revert when version is mismatch
    onChanOpenTry()
      正常系
        ✔ tokenTransferSourceVersion return normally
      準正常系
        ✔ should revert when caller is not ibc
        ✔ should revert when msg_.counterpartyVersion is mismatch

  Oracle
    version()
      正常系
        初期状態
          ✔ versionが返却されること
    addOracle()
      正常系
        初期状態
          ✔ oracleが登録されること
      準正常系
        初期状態
          ✔ oracleId = 0 を指定した場合、エラーがスローされること
          ✔ invokerに空アドレスを指定した場合、エラーがスローされること
    deleteOracle()
      正常系
        oracleが登録されている状態
          ✔ oracleが削除されること
      準正常系
        oracleが登録されている状態
          ✔ oracleId = 0 を指定した場合、エラーがスローされること
    set()
      正常系
        oracleが登録されている状態
          ✔ OracleValuesが設定されること
          ✔ 同一keyに対してOracleValuesが設定されている場合、イベントが発火されないこと
      準正常系
        oracleが登録されている状態
          ✔ 未登録invokerを指定した場合、エラーがスローされること
    setBatch()
      正常系
        oracleが登録されている状態
          ✔ 複数件OracleValuesが設定されること
      準正常系
        oracleが登録されている状態
          ✔ 未登録invokerを指定した場合、エラーがスローされること
          ✔ keysとvaluesの要素数が不一致の場合、エラーがスローされること
    get()
      正常系
        OracleValueが複数登録されている状態
          ✔ keyに紐づくOracleValueの値が返却されること
          ✔ 存在しないkeyを指定した場合、0x00が返却されること
          ✔ oracleId = 0 を指定した場合、エラーが返却されること
    getBatch()
      正常系
        OracleValueが複数登録されている状態
          ✔ keysに紐づくOracleValueの値が返却されること
          ✔ oracleId = 0 を指定した場合、エラーが返却されること

  Provider
    version()
      正常系
        ✔ versionを取得できること
    initialize()
      正常系
        ✔ should revert when initialized
    getProvider()
      正常系
        providerが登録されていない状態
          ✔ エラーが返されること
        provider, role, tokenが登録されている状態
          ✔ provider情報が取得できること
    getZone()
      正常系
        providerが登録されていない状態
          ✔ エラーが返されること
        providerが登録されている状態
          ✔ zone情報が取得できること
        providerのzoneIdが0の状態
          ✔ エラーが返されること
    getZoneName()
      正常系
        providerが登録されていない状態
          ✔ zoneNameは空が返されること
        providerが登録されている状態
          ✔ zoneNameが取得できること
    getProviderCount()
      正常系
        providerが登録されていない状態
          ✔ 0が取得できること
        providerが登録されている状態
          ✔ provider数が取得できること
    getAvailableIssuerIds()
      正常系
        provider, role, tokenが登録されている状態
          ✔ イシュアIDリストが取得できること
          ✔ 0件のイシュアIDリストが取得できること
    addProvider()
      正常系
        providerが登録されていない状態
          ✔ providerが登録できること
          ✔ versionを取得できること
      準正常系
        providerが登録されていない状態
          ✔ Admin権限ではない署名の場合、エラーがスローされること
          ✔ 署名期限切れの場合、エラーがスローされること
          ✔ 空providerId指定の場合、エラーがスローされること
          ✔ 署名無効の場合、エラーがスローされること
        providerが登録されている状態
          ✔ 既存のProvIDを指定した場合、エラーがスローされること
    addBizZone()
      準正常系
        providerId is not valid
          ✔ should revert when _providerId is 0x00
    addProviderRole()
      正常系
        providerが登録されている状態
          ✔ provider roleが登録できること
        roleが登録されている状態
          ✔ 同じproviderに対して複数providerRoleが登録できること
      準正常系
        providerが登録されている状態
          ✔ Admin権限ではない署名使用の場合、エラーがスローされること
          ✔ 署名期限切れの場合、エラーがスローされること
          ✔ 署名無効の場合、エラーがスローされること
          ✔ 未登録providerId指定の場合、エラーがスローされること
          ✔ 無効EOA(0x0)指定の場合、エラーがスローされること
    modZone()
      正常系
        mod zone normally
          ✔ should mod zone normally
      準正常系
        mod zone with invalid providerId
          ✔ should revert when providerId is invalid
          ✔ should revert when providerId is not registered
    addToken()
      正常系
        provider, roleが登録されている状態
          ✔ tokenが追加できること
      準正常系
        provider, roleが登録されている状態
          ✔ providerIdが違う場合、エラーがスローされること
          ✔ 無効署名の場合、エラーがスローされること
          ✔ タイムアウトが発生する場合、エラーがスローされること
        tokenが登録されている状態
          ✔ token重複登録の場合、エラーがスローされること
      Not normal
        provider not role
          ✔ should revert when provider not role
    modToken()
      正常系
        provider, role, tokenが登録されている状態
          ✔ tokenの要素(name, symbol)が変更できること
        tokenの要素が更新されている状態(name: BASE.TOKEN.TOKEN2.NAME, symbol: BASE.TOKEN.TOKEN2.SYMBOl)
          ✔ tokenの要素(name)が変更できること
        tokenの要素が更新されている状態(name: BASE.TOKEN.TOKEN1.NAME)
          ✔ tokenの要素(symbol)が変更できること
      準正常系
        provider, role, tokenが登録されている状態
          ✔ 未登録tokenIdで変更の場合、エラーがスローされること
          ✔ 署名無効の場合、エラーがスローされること
          ✔ タイムアウトが発生の場合、エラーがスローされること
      Not normal
        provider not role
          ✔ should revert when provider not role
    hasToken()
      正常系
        provider, role, tokenが登録されている状態
          ✔ tokenが追加済みの場合、trueが返されること
          ✔ 未登録tokenIdの場合、falseが返されること
          ✔ 空providerId指定の場合、falseが返されること
          ✔ 未登録providerIdの場合、falseが返されること
          ✔ 空tokenId指定の場合、falseが返されること
        tokenが無効の状態
          ✔ falseが返されること
    getTokenId()
      正常系
        providerが登録されていない状態
          ✔ エラーが返されること
        providerが登録されている状態
          ✔ tokenが登録されていない場合、エラーが返されること
        role, tokenが登録されている状態
          ✔ tokenIdが取得できること
    modProvider()
      正常系
        providerが登録されている状態
          ✔ providerが更新できること
      準正常系
        providerが登録されている状態
          ✔ Admin権限ではない署名の場合、エラーがスローされること
          ✔ 署名無効の場合、エラーがスローされること
          ✔ 未登録のproviderId場合、エラーがスローされること
    getToken()
      正常系
        provider, role, tokenが登録されている状態
          ✔ tokenの情報が取得できること
          ✔ 違うproviderIdで取得する場合、エラーが返されること
    hasProvider()
      正常系
        providerが登録されている状態
          ✔ 有効性確認フラグがtrueの場合、trueが返されること
          ✔ 有効性確認フラグがfalseの場合、trueが返されること
          ✔ 空providerId指定の場合、falseが返されること
          ✔ 未登録providerId指定の場合、falseが返されること
    checkRole()
      正常系
        provider, roleが登録されている状態
          ✔ Role付与済みの場合Trueが返されること
          ✔ Role付与済みでないの場合Falseが返されること
          ✔ 空providerId指定の場合、falseが返されること
          ✔ タイムアウトが発生する場合、falseが返されること
    getProviderAll()
      正常系
        providerが登録されていない状態
          ✔ 取得したprovider情報が初期値であること
        provider情報が1件登録されている状態
          ✔ provider情報が1件取得できること
        provider情報が1件zoneIdが複数登録されている状態
          ✔ zoneData情報が2件取得できること
    setProviderAll()
      正常系
        providerが登録されていない状態
          ✔ すべてのproviderが登録できること
      準正常系
        providerが登録されていない状態
          ✔ 署名無効の場合、エラーがスローされること
          ✔ 署名期限切れの場合、エラーがスローされること
          ✔ 異常な値が入力された時にfails

  checkAvailableIssuerIds()
    正常系
      provider, roleが登録されている状態
        ✔ 認可イシュアリストが取得できること
    準正常系
      provider, roleが登録されている状態
        ✔ ゾーンに紐付けしていないイシュアに対応するアカウントを指定した場合、エラーが返却されること
        ✔ ゾーンに紐付けしているイシュアが存在しない場合、エラーが返却されること

  RemigrationBackup
    version()
      正常系
        ✔ versionが取得できること
    backupValidators()
      正常系
        validatorが登録されていない状態
          ✔ 空リストが取得できること
        Validatorsが登録されている状態
          ✔ offset0, limit10を指定した場合、1ページ目1項目目から10件取得できること
          ✔ offset1, limit10を指定した場合、2ページ目1項目目から10件取得できること
          ✔ offset1, limit2を指定した場合、2ページ目2項目目から2件取得できること
          ✔ 最後の1件が取得できること
          ✔ limitが取得上限(100件)以下の場合、データが取得ができること (66ms)
          ✔ limitが0の場合、空リストが取得できること
          ✔ limitが取得上限(100件)より大きい場合、エラーが返されること
          ✔ offsetが登録されている件数以上の場合、エラーが返されること
          ✔ Admin権限がない場合、エラーが返されること
          ✔ 署名無効の場合、エラーが返されること
          ✔ 署名期限切れの場合、エラーが返されること
    backupProvider()
      正常系
        providerが登録されていない状態
          ✔ 取得したprovider情報が初期値であること
        provider情報が登録されている状態
          ✔ provider情報が取得できること
          ✔ 署名期限切れの場合、取得したprovider情報が初期値であること
          ✔ Admin権限ではない署名の場合、取得したprovider情報が初期値であること
    backupAccount()
      正常系
        accountが登録されていない状態
          ✔ 空リストが取得できること
        Accountsが登録されている状態
          ✔ offset0, limit20を指定した場合、1ページ目1項目目から20件取得できること (70ms)
          ✔ offset1, limit10を指定した場合、2項目目から10件取得できること
          ✔ offset1, limit2を指定した場合、3項目目から2件取得できること
          ✔ 最後の1件が取得できること
          ✔ limitが取得上限(1000件)以下の場合、データが取得ができること (70ms)
          ✔ limitが0の場合、空リストが取得できること
          ✔ limitが取得上限(1000件)より大きい場合、エラーが返されること
          ✔ offsetが登録されている件数以上の場合、エラーが返されること
          ✔ Admin権限がない場合、エラーが返されること
          ✔ 署名無効の場合、エラーが返されること
          ✔ 署名期限切れの場合、エラーが返されること
    backupFinancialZoneAccounts()
      正常系
        accountが登録されていない状態
          ✔ 空リストが取得できること
        Accountsが登録されている状態
          ✔ offset0, limit20を指定した場合、1ページ目1項目目から20件取得できること
          ✔ offset1, limit10を指定した場合、2項目目から10件取得できること
          ✔ offset1, limit2を指定した場合、3項目目から2件取得できること
          ✔ 最後の1件が取得できること
          ✔ limitが取得上限(1000件)以下の場合、データが取得ができること
          ✔ limitが0の場合、空リストが取得できること
          ✔ limitが取得上限(1000件)より大きい場合、エラーが返されること
          ✔ offsetが登録されている件数以上の場合、エラーが返されること
          ✔ Admin権限がない場合、エラーが返されること
          ✔ 署名無効の場合、エラーが返されること
          ✔ 署名期限切れの場合、エラーが返されること
    backupIssuers()
      正常系
        issuerが登録されていない状態
          ✔ 空リストが取得できること
        Issuerが登録されている状態
          ✔ offset0, limit10を指定した場合、1ページ目1項目目から10件取得できること
          ✔ offset1, limit10を指定した場合、2ページ目1項目目から10件取得できること
          ✔ offset1, limit2を指定した場合、3ページ目2項目目から2件取得できること
          ✔ 最後の1件が取得できること
          ✔ limitが取得上限(100件)以下の場合、データが取得ができること
          ✔ limitが0の場合、空リストが取得できること
          ✔ limitが取得上限(1000件)より大きい場合、エラーが返されること
          ✔ offsetが登録されている件数以上の場合、エラーが返されること
          ✔ Admin権限がない場合、エラーが返されること
          ✔ 署名無効の場合、エラーが返されること
          ✔ 署名期限切れの場合、エラーが返されること
    backupToken()
      正常系
        tokenが登録されていない状態
          ✔ 空のtoken情報が取得できること
        Tokenが登録されている状態
          ✔ 1件が取得できること
          ✔ Admin権限がない場合、エラーが返されること
          ✔ 署名無効の場合、エラーが返されること
          ✔ 署名期限切れの場合、エラーが返されること
      準正常系 - backupToken()エラーケース
        ✔ Admin権限がない場合、RV0001_ACTRL_BAD_ROLE エラーが返されること
        ✔ 署名無効の場合、backupToken()でエラーが返されること
        ✔ 署名期限切れの場合、backupToken()でエラーが返されること
    backupBusinessZoneAccounts()
      正常系
        businessZoneAccountsが登録されていない状態
          ✔ 空リストが取得できること
        businessZoneAccountsが登録されている状態
          ✔ offset0, limit10を指定した場合、1ページ目1項目目から3件取得できること
          ✔ 最初の1件が取得できること
          ✔ 最後の1件が取得できること
          ✔ limitが0の場合、空リストが取得できること
          ✔ limitが取得上限(1000件)より大きい場合、エラーが返されること
          ✔ offsetが登録されている件数以上の場合、エラーが返されること
          ✔ Admin権限がない場合、エラーが返されること
          ✔ 署名無効の場合、エラーが返されること
          ✔ 署名期限切れの場合、エラーが返されること

  Remigration
    version()
      正常系
        ✔ versionが取得できること
    restoreValidators()
      正常系
        初期状態
          ✔ 全てのissuers(20件)が登録できること (129ms)
      準正常系
        初期状態
          ✔ Admin権限がない場合、エラーがスローされること
          ✔ 署名無効の場合、エラーがスローされること
          ✔ 署名期限切れの場合、エラーがスローされること
          ✔ 異常な値が入力された時にfails
    restoreProvider()
      正常系
        providerが登録されていない状態
          ✔ すべてのproviderが登録できること
      準正常系
        providerが登録されていない状態
          ✔ 署名無効の場合、エラーがスローされること
          ✔ 署名期限切れの場合、エラーがスローされること
          ✔ 異常な値が入力された時にfails
    restoreAccounts()
      正常系
        初期状態
          ✔ 全てのaccounts(20件)が登録できること (137ms)
      準正常系
        初期状態
          ✔ Admin権限がない場合、エラーがスローされること (55ms)
          ✔ 署名無効の場合、エラーがスローされること (54ms)
          ✔ 署名期限切れの場合、エラーがスローされること (58ms)
          ✔ 異常な値が入力された時にfails
    restoreFinancialZoneAccounts()
      正常系
        初期状態
          ✔ 全てのaccounts(20件)が登録できること (62ms)
      準正常系
        初期状態
          ✔ Admin権限がない場合、エラーがスローされること
          ✔ 署名無効の場合、エラーがスローされること
          ✔ 署名期限切れの場合、エラーがスローされること
          ✔ 異常な値が入力された時にfails
    restoreIssuers()
      正常系
        初期状態
          ✔ 全てのissuers(20件)が登録できること (96ms)
      準正常系
        初期状態
          ✔ Admin権限がない場合、エラーがスローされること
          ✔ 署名無効の場合、エラーがスローされること
          ✔ 署名期限切れの場合、エラーがスローされること
          ✔ 異常な値が入力された時にfails
    restoreToken()
      正常系
        初期状態
          ✔ tokenが登録できること
      準正常系
        初期状態
          ✔ Admin権限がない場合、エラーがスローされること
          ✔ 署名無効の場合、エラーがスローされること
          ✔ 署名期限切れの場合、エラーがスローされること
          ✔ 異常な値が入力された時にfails
    restoreBusinessZoneAccounts()
      正常系
        初期状態
          初期状態
            ✔ 全てのbusinessZoneAccount(20件)が登録できること (40ms)
      準正常系
        初期状態
          ✔ businessZoneAccountが登録されないこと
          ✔ Admin権限がない場合、エラーがスローされること
          ✔ 署名無効の場合、エラーがスローされること
          ✔ 署名期限切れの場合、エラーがスローされること
          ✔ 異常な値が入力された時にfails
      準正常系
        初期状態
          ✔ Admin権限がない場合、エラーがスローされること
          ✔ 署名無効の場合、エラーがスローされること
          ✔ 署名期限切れの場合、エラーがスローされること
          ✔ 異常な値が入力された時にfails

  RenewableEnergyToken
    version()
      正常系
        ✔ versionが返されること
    mint()
      正常系
        accountが登録されている状態
          ✔ NFTが新規でMintされること /* DCPF-21262
      準正常系
        TokenがMintされている状態
          ✔ 登録済みtokenIdを指定した場合にエラーがスローされること /* DCPF-21262
          ✔ tokenIdが0x00の場合、RV0015_RETOKEN_INVALID_VAL エラーがスローされること
    getToken()
      正常系
        TokenがMintされている状態
          ✔ Tokenが返されること /* DCPF-21262
      準正常系
        TokenがMintされている状態
          ✔ 存在しないTokenIdを指定した場合、例外が発生すること
          ✔ 無効なTokenIdを指定した場合、例外が発生すること
    getTokenCount()
      正常系
        初期状態
          ✔ 空の配列が返されること
        TokenがMintされている状態
          ✔ 1件の配列が返されること
    getTokenList()
      正常系
        TokenがMintされていない状態
          ✔ 空の配列が返されること
        getRenewableEnergyTokenが登録されている状態 /* DCPF-21262
          ✔ offset0, limit10を指定した場合、1要素目から10件取得できること
          ✔ 複数のgetTokenList呼び出しが同時に行われた場合、それぞれ正しい結果を返すこと
          ✔ offset1, limit10を指定した場合、2要素目から10件取得できること
          ✔ offset2, limit2を指定した場合、3要素目から2件取得できること
          ✔ 最後の1件が取得できること
          ✔ limitが取得上限(99件)以下の場合、データが取得ができること (51ms)
          ✔ limitが0の場合、空リストが取得できること
          ✔ 所有トークンのうち一つのtokenを別ユーザに移転している場合、トークンの総数が減少すること
          ✔ 複数のtokenを別ユーザーに移転した場合、移転したtokenが別ユーザの所有トークンリストとして取得できること
        RenewableEnergyTokenが追加で5つMintされている状態
          ✔ mintを行ったユーザーが自身がmintしたtokenの一覧を取得できること
          ✔ mintを行ったユーザーが自身がmintしたtokenの一覧を取得できること(offset:2, limit:3)
          ✔ mintを行ったユーザーが自身がmintしたtokenの一覧を取得できること(offset:2, limit:10)
          ✔ mintを行ったユーザーが自身がmintしたtokenの一覧を取得できること(offset:4, limit:1)
          ✔ mintを行ったtokenがtransferされた場合でも、mintしたtokenの一覧を取得できること
      準正常系
        getRenewableEnergyTokenが登録されている状態 /* DCPF-21262
          ✔ 存在しないアカウントを指定した場合、エラーが返却されること
          ✔ 不正なアカウントIDを指定した場合、エラーが返却されること
          ✔ limitが取得上限(1000件)より大きい場合、エラーが返されること
          ✔ offsetが登録されたtokenIdより大きい場合、エラーが返されること
          ✔ offsetが登録されている件数以上の場合、エラーが返されること
          ✔ offsetが登録されている件数と同じ場合、エラーが返されること
    transfer
      正常系
        Tokenが一つMintされている状態
          ✔ Tokenが移転されること
      準正常系
        Tokenが一つMintされている状態
          ✔ 移転先と移転元が同じである場合は移転できないこと
          ✔ 移転元がNFTを所有していない場合は移転できないこと
          ✔ 存在しないNFTを指定した場合は移転できないこと
    customTransfer
      正常系
        Tokenが一つMintされている状態
          ✔ CustomTransferを実行した時に、Tokenが移転されること
          ✔ miscValue1がrenewableではない場合、通常の移転が動作すること
          ✔ miscValue2に二つのtokenIdを含めると、二つ分移転が完了すること
      準正常系
        Tokenが一つMintされている状態
          ✔ miscValue2が空の場合、エラーが出力されること
          ✔ ownerAccountId != toAccountIdではない場合、エラーが出力されること
          ✔ 存在しないNFTを指定した場合は移転できないこと
          ✔ miscValue2に複数のtokenIdが含まれている状態で、片方のtokenIdが存在しない場合に移転が失敗すること
          ✔ miscValue2に複数のtokenIdが含まれている状態で、片方のtokenIdをfromAccountが所有していない場合に移転に失敗すること
    checkTransaction
      正常系
        Tokenが一つMintされている状態
          ✔ Tokenが移転可能かどうか確認できること
          ✔ miscValue2に二つのtokenIdを含めると、二つ分のチェックが完了すること
      準正常系
        Tokenが一つMintされている状態
          ✔ MiscValue1が"renewable"でない場合、エラーを返却する
          ✔ MiscValue2に含まれるtokenIdの要素数が100を超える場合、エラーを返却する
          ✔ MiscValue2に含まれる要素の値が32bytesを超える場合、エラーを返却する
          ✔ トークンIDが存在しない場合、エラーを返却する
          ✔ ownerAccountIdとfromAccountIdが一致しない場合、エラーを返却する
          ✔ toAccountIdとfromAccountIdが一致する場合、エラーを返却する
          ✔ sendAccountIdが存在しない場合、エラーを返却する
          ✔ toAccountIdが存在しない場合、エラーを返却する
    backupRenewableEnergyTokens()
      正常系
        RenewableEnergyTokenが登録されていない状態
          ✔ 空リストが取得できること
        RenewableEnergyTokenが登録されている状態
          ✔ offset0, limit10を指定した場合、1ページ目1項目目から10件取得できること
          ✔ offset1, limit10を指定した場合、2ページ目1項目目から10件取得できること
          ✔ offset1, limit2を指定した場合、2ページ目2項目目から2件取得できること
          ✔ 最後の1件が取得できること
          ✔ limitが取得上限(1000件)以下の場合、データが取得ができること
          ✔ limitが0の場合、空リストが取得できること
      準正常系
        RenewableEnergyTokenが登録されている状態
          ✔ limitが取得上限(1000件)より大きい場合、エラーが返されること
          ✔ offsetが登録されている件数以上の場合、エラーが返されること
          ✔ Admin権限がない場合、エラーが返されること
          ✔ 署名無効の場合、エラーが返されること
          ✔ 署名期限切れの場合、エラーが返されること
    restoreRenewableEnergyTokens()
      正常系
        初期状態
          ✔ 全てのRenewableEnergyToken(20件)が登録できること
      準正常系
        初期状態
          ✔ Admin権限がない場合、エラーがスローされること
          ✔ 署名無効の場合、エラーがスローされること
          ✔ 署名期限切れの場合、エラーがスローされること
          ✔ 異常な値が入力された時にfails
    backupTokenIdsByAccountIds()
      正常系
        RenewableEnergyTokenが登録されていない状態
          ✔ 空リストが取得できること
        RenewableEnergyTokenが登録されている状態
          ✔ Tokenリストが取得できること
      準正常系
        RenewableEnergyTokenが登録されている状態
          ✔ Admin権限がない場合、エラーが返されること
          ✔ 署名無効の場合、エラーが返されること
          ✔ 署名期限切れの場合、エラーが返されること
    restoreTokenIdsByAccountId()
      正常系
        初期状態
          ✔ TokenIdsByAccountId(20件)が登録できること
      準正常系
        初期状態
          ✔ Admin権限がない場合、エラーがスローされること
          ✔ 署名無効の場合、エラーがスローされること
          ✔ 署名期限切れの場合、エラーがスローされること
          ✔ 異常な値が入力された時にfails

  StringUtils
    stringToBytes32()
      正常系
        ✔ stringの文字列をbytes32型の文字列に変換すること
        ✔ 32文字のstringをbytes32型の文字列に変換すること
        ✔ 空のstringをbytes32型の文字列に変換できること
      準正常系
        ✔ 33文字のstringをbytes32型に変換しようとするとリバートすること
    slice()
      正常系
        ✔ 文字列をデリミタで分割すること
        ✔ デリミタが文字列の先頭にある場合
        ✔ デリミタが文字列の末尾にある場合
        ✔ デリミタが連続している場合
        ✔ デリミタがソース文字列と同じ場合
        ✔ デリミタがマルチバイト文字の場合
      準正常系
        ✔ 空の文字列を分割する場合、リバートすること
        ✔ デリミタが空文字列の場合、リバートすること
        ✔ デリミタがソース文字列より長い場合、リバートすること
        ✔ ソース文字列とデリミタが空の場合、リバートすること
    substring()
      正常系
        ✔ 指定された範囲の文字列を取得すること
        ✔ startIndexが0の場合
        ✔ endIndexが文字列の長さと等しい場合
        ✔ startIndexとendIndexが同じ場合、空文字列を返すこと
        ✔ マルチバイト文字列でのsubstring
      準正常系
        ✔ endIndexが文字列の長さを超える場合、リバートすること
        ✔ startIndexが文字列の長さを超える場合、リバートすること
        ✔ startIndexがendIndexより大きい場合、リバートすること

  Token
    version()
      正常系
        ✔ versionが返されること
    initialize()
      正常系
        ✔ should revert when initialized
    addToken
      準正常系
        初期状態
          ✔ 呼び出し元がProviderではない場合、エラーがスローされること
        tokenが登録されている状態
          ✔ should revert when tokenId is not valid
    addTotalSupply
      準正常系
        overflow handling
          ✔ should revert when totalSupply is overflow
    approve()
      正常系
        accountが登録されている状態
          ✔ allowanceが設定されること
        accountにallowanceが設定されている状態
          ✔ 異なる宛先に対しapproveを行なった場合、異なるallowanceが設定されること
          ✔ 金額を変更しapproveを行なった場合、allowanceが更新されること
    getAllowance()
      正常系
        allowanceが設定されている状態
          ✔ approveで指定した額が返されること
          ✔ approveで設定していないaccountIdを指定した場合、0が返されること
          ✔ 未登録accountIdをspenderに指定した場合、エラーがスローされること
          ✔ 未登録accountIdをownerに指定した場合、エラーがスローされること
      準正常系
        validatorが登録されていない状態
          ✔ validatorIdが未入力である場合、エラーがスローされること
          ✔ 存在しないvalidatorを指定した場合、エラーがスローされること
    getAllowanceList()
      正常系
        accountが登録されていない状態
          ✔ 未登録accountIdをownerに指定した場合、エラーがスローされること
          ✔ accountIdが空の場合、エラーがスローされること
        allowanceが設定されている状態
          ✔ 複数のgetAllowanceList呼び出しが同時に行われた場合、それぞれ正しい結果を返すこと
          ✔ offset0, limit10を指定した場合、1要素目から10件取得できること
          ✔ offset1, limit10を指定した場合、2要素目から10件取得できること
          ✔ offset1, limit2を指定した場合、2要素目から2件取得できること
          ✔ 最後の1件が取得できること
          ✔ limitが0の場合、空リストが取得できること
          ✔ limitを登録数以上に指定した場合、登録されている要素数の範囲内でのみ取得できること
          ✔ limitが取得上限(100件)より大きい場合、エラーが返されること
          ✔ offsetが登録されている件数以上の場合、エラーが返されること
          ✔ offsetが配列と同じ長さの場合、エラーが返されること
      準正常系
        spenderが登録されていない状態
          ✔ spenderが登録されていない場合、空リストが取得できること
    getBalanceList()
      正常系
        provider, providerRole, issuer, validator, token, account, 二つのbusinessZoneAccountが登録されている状態
          ✔ BusinessZoneのアカウント残高の一覧が取得できること
          ✔ 存在しないアカウントIDで実行した場合、エラーが返却されること
          ✔ 空のアカウントIDで実行した場合、エラーが返却されること
      準正常系
        account is terminated
          ✔ should return data of accountId without any changed when account is terminated 
    mint()
      正常系
        accountが登録されている状態
          ✔ 指定された額がMintされること
        mintがされている状態(balance(100), totalSupply(100))
          ✔ Mintされた額が加算されること、totalSupplyが加算されること
          ✔ (amount=0のため)値が変動されないこと
          ✔ 異なるaccountに対してMintした場合でも、totalSupplyが加算されること
        一回の操作限度額を200, 累積限度額を300のアカウントを作成した場合
          ✔ 日跨りで一日の累積限度額を2回目の発行で超えている場合、balancが加算されること
      準正常系
        issuerRoleが登録されている状態
          ✔ 空accountIdを指定した場合、エラーがスローされること
          ✔ 空issuerIdを指定した場合、エラーがスローされること
      複数BizZoneに残高を持つアカウントへのMintテスト
        複数のBizZoneに残高がある状態
          ✔ 複数のBizZone残高を含むAfterBalanceEmittedイベントが発行されること
      Not normal
        getZone return some error
          ✔ should revert when token is overflow
    burn()
      正常系
        mintがされている状態(balance(account0: 200, account1: 100), totalSupply(300))
          ✔ Burnされた額が減算されること、totalSupplyが減算されること
        burnがされている状態(balance(account0: 100, account1: 100), totalSupply(200))
          ✔ (amount=0のため)値が変動されないこと
          ✔ 異なるaccountに対してBurnした場合でも、Burnされた額が減算されること、totalSupplyが減算されること
        一回の操作限度額を50, 累積限度額を400のアカウントを作成した場合
          ✔ 日跨りで一日の累積限度額を3回目の償却で超えている場合、残高が減算されること
      準正常系
        mintがされている状態(balance(account0: 200, account1: 100), totalSupply(300))
          ✔ 空accountIdを指定した場合、エラーがスローされること
          ✔ 空issuerIdを指定した場合、エラーがスローされること
          ✔ balanceが不足している場合、エラーがスローされること
    burnCancel()
      正常系
        burnがされている状態
          ✔ BurnCancelされた額が加算されること、totalSupplyが加算されること
      準正常系
        issuerRoleが登録されていない状態
          ✔ issuer権限がない場合、エラーをスローすること
        burnがされている状態
          ✔ 空accountIdを指定した場合、エラーがスローされること
          ✔ 空issuerIdを指定した場合、エラーがスローされること
          ✔ 未登録accountIdを指定した場合、エラーがスローされること
          ✔ issuerに紐付けられていないaccountIdを指定した場合、エラーがスローされること
          ✔ 署名が不正である場合、エラーがスローされること
          ✔ 署名期限切れの場合、エラーがスローされること
        accountが解約状態の場合
          ✔ 解約済accountIdを指定した場合、エラーがスローされること
      Not normal
        getZone return some error
          ✔ should revert when totalSupply is overflow
    transferSingle()
      正常系
        account, カスタムコントラクトが登録されている状態
          ✔ miscValueが空指定の場合、指定されたamountでTransferが実行されること
          ✔ miscValue1にcustomTransferで設定している値を指定した場合、customTransferで指定されたamountでTransferが実行されること
          ✔ miscValue2に値が入力された場合、指定されたamountでTransferが実行されること
        allowanceが設定されている状態
          ✔ (senderとfromが異なるaccountを指定した場合)allowanceが設定されている場合、指定されたamountでTransferが実行されること
        allowanceに無制限送金を許可する値が設定されている状態
          ✔ (senderとfromが異なるaccountを指定した場合)allowanceにallowanceに無制限送金を許可する値が設定されている場合、Transferを実行してもAllowance量が変化しないこと
        allowanceに無制限送金を許可する値より1小さい値が設定されている状態
          ✔ (senderとfromが異なるaccountを指定した場合)allowanceにallowanceに無制限送金を許可する値が設定されている場合、Transferを実行した後に許可額が送金額分減額されること
        一回の操作限度額を1000, 累積限度額を5000のアカウントを作成した場合
          ✔ 日跨りで一日の累積限度額を2回目の送金で超えている場合、balancが加算されること
      準正常系
        accountが登録されている状態
          ✔ balanceが不足している場合、エラーがスローされること
        allowanceが設定されている状態
          ✔ (senderとfromが異なるaccountを指定した場合)allowanceが不足している場合、エラーがスローされること
          ✔ (senderとfromが異なるaccountを指定した場合)fromで指定したaccountのbalanceが不足している場合、エラーがスローされること
          ✔ AccountLib should revert when allowance is not enough from fake token call
      Normal case
        zoneId is not _FINANCIAL_ZONE
          ✔ should sync transfer and run normally
    setTokenEnabled()
      正常系
        tokenが登録されている状態
          ✔ enabledがfalseに変更されること
          ✔ enabledがtrueに変更されること
      準正常系
        providerRoleが登録されていない状態
          ✔ provider権限がない場合、エラーがスローされること
        tokenが登録されている状態
          ✔ 未登録providerIdを指定した場合、エラーがスローされること
          ✔ 未登録tokenIdを指定した場合、エラーがスローされること
          ✔ 署名が不正である場合、エラーがスローされること
          ✔ 署名期限切れの場合、エラーがスローされること
    subTotalSupply
      準正常系
        Underflow handling
          ✔ should revert when token underflow

  Token2
    hasToken()
      正常系
        tokenが登録されている状態
          ✔ 未登録tokenIdを指定した場合、エラーが返されること
          ✔ tokenが存在する場合、trueが取得できること
          ✔ 空tokenIdを指定した場合、エラーが返されること
        tokenが無効状態の場合
          ✔ 有効状態チェックフラグがfalseの場合、trueが取得できること
          ✔ 有効状態チェックフラグがtrueの場合、、エラーが返されること
    getToken
      準正常系
        初期状態
          ✔ 呼び出し元がProviderではない場合、エラーがスローされること
    checkApprove()
      正常系
        アカウントが登録されている状態
          ✔ Approveが可能かどうか確認できること
      準正常系
        アカウントが登録されている状態
          ✔ 存在しないバリデータを指定した場合、エラーが返却されること
          ✔ OwnerAccountのステータスがアクティブではない場合、エラーが返却されること
          ✔ SenderAccountのステータスがアクティブではない場合、エラーが返却されること
          ✔ バリデータに紐づいていないアカウントの場合、エラーが返却されること
          ✔ バリデータ署名が不正である場合、エラーが返却されること
          ✔ バリデータ署名が有効期限切れである場合、エラーが返却されること
          ✔ アカウント署名が無効である場合、エラーが返却されること
          ✔ 空のアカウント署名が無効である場合、エラーが返却されること
          ✔ should revert when account is not registered

  TransferProxy
    version()
      正常系
        ✔ versionが取得できること
    addRule
      正常系
        初期状態
          ✔ Ruleを1番目に追加する
          ✔ Ruleを2番目に追加する
          ✔ Ruleを3番目に追加する
          ✔ Ruleを0番目に再追加する
          ✔ Ruleを2番目に再追加する
      異常系
        初期状態
          ✔ 初回登録時にPosition指定が0でない場合はエラー
        Ruleが2件登録されている状態
          ✔ 既に登録されているRule(_nextRule)の場合はエラー
          ✔ 既に登録されているRule(_firstRule)の場合はエラー
          ✔ 範囲外のPositionを指定して登録する場合はエラー
    isRegistered
      正常系
        Ruleが2件登録されている状態
          ✔ 登録済の1件目のAddressを確認する
          ✔ 登録済の2件目のAddressを確認する
          ✔ 未登録のAddressの場合はFalseを返す
    deleteRule
      正常系
        Ruleが5件登録されている状態
          ✔ 5番目(_nextRule)のaddressを削除
          ✔ 1番目のaddressを削除
          ✔ 3番目の内、2番目のルールを削除する
          ✔ 2番目の内、2番目のルールを削除する
          ✔ 最後のルールを削除する
        異常系
          初期状態
            ✔ Ruleが未登録の場合はエラー
          Ruleが5件登録されている状態
            ✔ 存在しないRuleを指定した場合はエラー
    findAll
      正常系
        初期状態
          ✔ 未登録の場合は空を返す
        Ruleが5件登録されている状態
          ✔ ルールを全て取得する
    clearRule
      正常系
        初期状態
          ✔ 未登録の状態でClearする
        Ruleが5件登録されている状態
          ✔ 全てのRuleをClearする
    customTransfer
      正常系
        accountが登録されている状態
          ✔ カスタムコントラクトが登録されていない状態でCustomTransferを実行してもEventが発火されない
        カスタムコントラクトが登録されている状態
          ✔ カスタムコントラクト1に対してCustomTransferを実行する
          ✔ カスタムコントラクト2に対してCustomTransferを実行する
          ✔ カスタムコントラクト3に対してCustomTransferを実行する
          ✔ 該当しないカスタムコントラクトを実行し、通常のTransferが実行される事を確認する。

  Validator
    version()
      正常系
        ✔ versionが取得できること
    addValidator()
      正常系
        provider, providerRole, issuerが登録されている状態
          ✔ validatorが登録できること
          ✔ validatorが登録できること(名前が空文字指定)
        付加領域
          provider, providerRoleが登録されている状態
            ✔ 登録されていないissuerを指定しても、validatorが登録できること
        Not normal
          issuer is not validator in _FINANCIAL_ZONE
            ✔ should revert when issuerId is not validator
            ✔ should revert when issuerId is not exist
      準正常系
        providerが登録されていない状態
          ✔ providerが未登録の場合、エラーがスローされること
        provider, providerRole, issuerが登録されている状態
          ✔ Admin権限でない署名の場合、エラーがスローされること
          ✔ 署名期限切れの場合、エラーがスローされること
          ✔ 署名が無効の場合、エラーがスローされること
          ✔ 空validatorIdを指定した場合、エラーがスローされること
          ✔ 空issuerIdを指定した場合、エラーがスローされること
        validatorが登録されている状態
          ✔ 同一validatorIdを指定した場合、エラーがスローされること
          ✔ 同一IssuerIDを指定した場合、エラーがスローされること
    addValidatorRole()
      正常系
        provider, providerRole, issuer, validatorが登録されている状態
          ✔ validatorRoleが追加できること
          ✔ validatorRoleが別のアカウントに追加できること
      準正常系
        provider, providerRole, issuer, validatorが登録されている状態
          ✔ Admin権限でない署名の場合、エラーがスローされること
          ✔ validatorIdが不正の場合、エラーがスローされること
          ✔ 署名期限切れの場合、エラーがスローされること
          ✔ 署名が無効の場合、エラーがスローされること
          ✔ 無効EOA(0x0)の場合、エラーがスローされること
    addAccount()
      正常系
        provider, providerRole, issuer, validator, tokenが登録されている状態
          ✔ accountが追加できること
      準正常系
        初期状態
          ✔ validatorが未登録の場合、エラーがスローされること
        accountが登録されている状態
          ✔ 空validatorIdを指定した場合、エラーがスローされること
        accountId not exist or registered
          ✔ should revert when accountId not exist
        accountが登録されている状態
          ✔ account重複登録の場合、エラーがスローされること
    addValidatorAccountId()
      正常系
        provider, providerRole, issuer, validator, token, accountが登録されている状態
          ✔ Validator管理のアカウントIDを追加できること
      準正常系
        初期状態
          ✔ validatorが未登録の場合、エラーがスローされること
        provider, providerRole, issuer, validatorが登録されている状態
          ✔ accountが未登録の場合、エラーがスローされること
        accountが登録されている状態
          ✔ 空validatorIdを指定した場合、エラーがスローされること
          ✔ 空accountIdを指定した場合、エラーがスローされること
          ✔ 紐づいていないaccountIdを指定した場合、エラーがスローされること
      Not normal
        account exist in contract account but not in validator
          ✔ should revert when account exist in contract account but not in validator
    syncAccount()
      正常系
        provider, providerRole, issuer, validator, tokenが登録されている状態
          ✔ syncAccountでaccountが追加できること(approvalAmountが0)
          ✔ syncAccountでaccountが追加できること(approvalAmountが許可額上限値)
          ✔ syncAccountでaccountが解約できること
      準正常系
        provider, providerRole, issuer, validator, token, accountが登録されている状態
          ✔ アカウント申し込み時、accountが既に紐付けられている場合、エラーがスローされること
          ✔ アカウント解約申し込み時、accountが存在しない場合、エラーがスローされること
        accountStatus input is terminated
          ✔ should emit event SyncAccount but no data change when accountStatus input is terminated
    modValidator()
      正常系
        provider, providerRole, issuer, validatorが登録されている状態
          ✔ validatorが更新できること
      準正常系
        provider, providerRole, issuer, validatorが登録されている状態
          ✔ 未登録validatorIdを指定した場合、エラーがスローされること
          ✔ Admin権限でない署名の場合、エラーがスローされること
          ✔ 署名が無効の場合、エラーがスローされること
    modAccount
      正常系
        accountが登録されていない状態
          ✔ Accountが存在しない場合、エラーがスローされること
    hasValidator()
      正常系
        provider, providerRole, issuer, validatorが登録されている状態
          ✔ validatorが存在すること
          ✔ 空validatorIdを指定した場合、エラーが返されること
          ✔ 未登録validatorIdを指定した場合、エラーが返されること
    setTerminated()
      正常系
        provider, providerRole, issuer, validator, token, accountが登録されている状態
          ✔ accountの解約フラグを更新できること
      準正常系
        provider, providerRole, issuer, validator, token, accountが登録されている状態
          ✔ 未登録validatorIdを指定した場合、エラーが返されること
          ✔ accountIdが存在しない場合、エラーがスローされること
          ✔ should revert when accountId balance is not 0
    getValidator()
      正常系
        provider, providerRole, issuer, validatorが登録されている状態
          ✔ validator情報が取得できること
          ✔ 未登録validatorIdを指定した場合、エラーが返されること
    getValidatorCount()
      正常系
        provider, providerRole, issuer, validatorが登録されている状態
          ✔ validator数が取得できること
    getValidatorId()
      正常系
        provider, providerRole, issuer, validatorが登録されている状態
          ✔ validatorIdが取得できること
          ✔ 無効状態のvalidatorIdが取得できること
          ✔ 範囲外のIndex指定した場合、エラーが返されること
    hasAccount()
      正常系
        provider, providerRole, issuer, validator, token, accountが登録されている状態
          ✔ accountIdが存在すること
          ✔ 空accountIdを指定した場合、エラーが返されること
          ✔ 紐付けられていないアカウントを指定した場合、エラーが返されること
      準正常系
        provider, providerRole, issuer, validator, token, accountが登録されている状態
          ✔ validatorIdが不正の場合、エラーがスローされること
          ✔ validatorIdが存在しない場合場合、エラーがスローされること
    getValidatorList()
      正常系
        validatorが登録されていない状態
          ✔ 空リストが取得できること
        provider, providerRole, issuer, validatorが登録されている状態
          ✔ 複数のgetValidatorList呼び出しが同時に行われた場合、それぞれ正しい結果を返すこと
          ✔ offset0, limit10を指定した場合、1要素目から10件取得できること
          ✔ offset1, limit10を指定した場合、2要素目から10件取得できること
          ✔ offset2, limit2を指定した場合、3要素目から2件取得できること
          ✔ 最後の1件が取得できること
          ✔ limitが取得上限(100件)以下の場合、データが取得ができること
          ✔ limitが0の場合、空リストが取得できること
          ✔ limitを登録数以上に指定した場合、登録されている要素数の範囲内でのみ取得できること
          ✔ limitが取得上限(100件)より大きい場合、エラーが返されること
          ✔ offsetが登録されている件数以上の場合、エラーが返されること
          ✔ offsetが登録されている件数と同じ場合、エラーが返されること
    getAccountList()
      正常系
        accountが登録されていない状態
          ✔ Accountが登録されていない場合、空リストが取得できること
          ✔ should return empty when sortOrder is _DESC_SORT
        provider, providerRole, issuer, validator, token, accountが登録されている状態
          ✔ 複数のgetIssuerList呼び出しが同時に行われた場合、それぞれ正しい結果を返すこと
          ✔ sortOrderにDESCを指定し、offset0, limit20を指定した場合、20要素目から逆順に20件取得できること
          ✔ offset0, limit10を指定した場合、1要素目から10件取得できること
          ✔ sortOrderにDESCを指定し、offset5, limit10を指定した場合、15要素目から逆順に10件取得できること
          ✔ offset2, limit2を指定した場合、3要素目から2件取得できること
          ✔ 最後の1件が取得できること
          ✔ limitが取得上限(100件)以下の場合、accountリストが取得できること (limit 100)
          ✔ limitが0の場合、空リストが取得できること
          ✔ limitを登録数以上に指定した場合、登録されている要素数の範囲内でのみ取得できること
          ✔ limitが取得上限(100件)より大きい場合、エラーが返されること
          ✔ offsetが登録されている件数以上の場合、エラーが返されること
          ✔ offsetが登録されている件数と同じの場合、エラーが返されること
          ✔ sortOrderにDESCを指定した場合、accountリストが逆順で取得できること
    getAccountAll()
      正常系
        provider, providerRole, issuer, validator, token, account, 二つのbusinessZoneAccountが登録されている状態
          ✔ 紐づくBusinessZone情報も含め、アカウントの全情報が取得できること
          ✔ 存在しないアカウントIDで実行した場合、エラーが返却されること
          ✔ 空のアカウントIDで実行した場合、エラーが返却されること
    setBizZoneTerminated()
      正常系
        provider, providerRole, issuer, validator, token, account, 1つのbusinessZoneAccountが解約申込となっている状態
          ✔ FinZone管理のBizZoneアカウントを解約できること
      準正常系
        provider, providerRole, issuer, validator, token, account, 1つのbusinessZoneAccountが解約申込となっている状態
          ✔ 存在しないアカウントを指定した場合、エラー終了すること
          ✔ should revert when zoneId is invalid
    getZoneByAccountId()
      正常系
        provider, providerRole, issuer, validator, token, account, 二つのbusinessZoneAccountが登録されている状態
          ✔ zone情報のリストが取得できること
    getAccount()
      正常系
        provider, providerRole, issuer, validator, token, accountが登録されている状態
          ✔ validatorに紐づくaccount情報が取得できること
          ✔ 空accountIdを指定した場合、エラーが返されること
          ✔ 未登録accountIdを指定した場合、エラーが返されること
        accountが解約状態
          ✔ アカウントの解約フラグが取得できること
      正常系_BizZoneの場合
        provider, providerRole, issuer, validator, token, accountが登録されている状態(BizZoneとして登録)
          ✔ validatorに紐づくaccount情報が取得できること(Limit情報なし)
          ✔ 空accountIdを指定した場合、エラーが返されること
          ✔ 未登録accountIdを指定した場合、エラーが返されること
        accountが解約状態
          ✔ アカウントの解約フラグが取得できること
      準正常系
        validator.hasAccount return error
          ✔ should return empty data and error message when validatorId is not validator
          ✔ should return empty data and error message when validatorId is not exist
          ✔ should return empty data and error message when accountId is not validator
          ✔ should return empty data and error message when accountId is not exist
    getDestinationAccount()
      正常系
        accountが登録されている状態
          ✔ アカウント名を取得できる
      準正常系
        accountが登録されていない状態
          ✔ 存在しないアカウントの場合、エラーが返却される
          ✔ アカウントIDが空の場合、エラーが返却される
        accountが凍結状態
          ✔ アカウントが凍結状態の場合であっても、アカウント名を取得できる
        accountが解約状態
          ✔ アカウントが解約状態の場合、エラーが返却される
    getValidatorAccountId()
      正常系
        provider, providerRole, issuer, validator, token, accountが登録されている状態
          ✔ Validator管理のアカウントIDを取得できること
      準正常系
        初期状態
          ✔ validatorが未登録の場合、エラーが返却されること
        provider, providerRole, issuer, validatorが登録されている状態
          ✔ accountが未登録の場合、エラーがスローされること
        accountが登録されている状態
          ✔ 空validatorIdを指定した場合、エラーがスローされること
    hasValidatorRole()
      正常系
        Validatorが登録されている状態
          ✔ roleがある場合、trueが取得できること
          ✔ roleがない場合、falseが取得できること
          ✔ 空validatorIdを指定した場合、エラーが返されること
          ✔ 署名期限切れの場合、エラーが返されること
        Validatorが無効の状態
          ✔ roleがある場合、trueが取得できること
    getValidatorAll()
      正常系
        Validatorsが登録されている状態
          ✔ 登録したValidatorsを取得できること (69ms)
    setValidatorAll()
      正常系
        初期状態
          ✔ 全てのissuers(20件)が登録できること
      準正常系
        初期状態
          ✔ Admin権限がない場合、エラーがスローされること
          ✔ 署名無効の場合、エラーがスローされること
          ✔ 署名期限切れの場合、エラーがスローされること
          ✔ 異常な値が入力された時にfails

  getAccountAllList()
    正常系
      accountが登録されていない状態
        ✔ Accountが登録されていない場合、空リストが取得できること
      provider, providerRole, issuer, validator, token, accountが登録されている状態
        ✔ 複数のgetIssuerList呼び出しが同時に行われた場合、それぞれ正しい結果を返すこと
        ✔ sortOrderにDESCを指定し、offset0, limit20を指定した場合、20要素目から逆順に20件取得できること
        ✔ offset0, limit10を指定した場合、1要素目から10件取得できること
        ✔ offset2, limit2を指定した場合、3要素目から2件取得できること
        ✔ 最後の1件が取得できること
        ✔ limitが取得上限(100件)以下の場合、accountリストが取得できること (limit 100) (38ms)
        ✔ limitが0の場合、空リストが取得できること
        ✔ limitを登録数以上に指定した場合、登録されている要素数の範囲内でのみ取得できること
        ✔ limitが取得上限(100件)より大きい場合、エラーが返されること
        ✔ offsetが登録されている件数以上の場合、エラーが返されること
        ✔ offsetが登録されている件数と同じの場合、エラーが返されること


  1210 passing (17s)
